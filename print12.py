"""
ملف طباعة الاستدعاءات - print12.py
====================================

هذا الملف مسؤول عن طباعة استدعاءات المترشحين (استدعاء واحد في كل صفحة).

** المصدر الوحيد للبيانات **
جدول 'جدولة_الامتحان' في قاعدة البيانات هو المصدر الوحيد والموثوق
لجميع بيانات جدولة الامتحانات المستخدمة في هذا الملف.

جميع الاستعلامات تتم من خلال هذا الجدول فقط.
"""

import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

# ================= إعدادات عامة =================
# الجدول الأول: 4 أعمدة، 6 صفوف (بدلاً من 6 أعمدة وصفين)
COL_WIDTHS_TABLE1 = [70, 30, 70, 25]  # عرض الأعمدة الأربعة للجدول الأول
# الجدول الثالث (أستاذ، تخصص، توقيع) أسفل الصفحة
COL_WIDTHS_TABLE3 = [30, 40, 35]
# الجدول الرابع (رئيس المركز، توقيع) أسفل الصفحة
COL_WIDTHS_TABLE4 = [35, 45]

# إضافة إعدادات للهوامش (بالملليمتر)
PAGE_MARGIN_TOP = 0.2     # الهامش العلوي للصفحة
PAGE_MARGIN_BOTTOM = 0.2  # الهامش السفلي للصفحة
PAGE_MARGIN_LEFT = 10     # الهامش الأيسر للصفحة
PAGE_MARGIN_RIGHT = 10    # الهامش الأيمن للصفحة

# تحويل النقاط إلى ملليمتر
PT_TO_MM = 0.3528

# إعدادات أحجام المربعات النصية
LOGO_W_PT, LOGO_H_PT = 200, 80
BOX1_W_PT = 555  # مربع العنوان الرئيسي بعرض أكبر
TITLE_H_PT = 40

# تحويل الأحجام من نقاط إلى ملليمتر
LOGO_W = LOGO_W_PT * PT_TO_MM
LOGO_H = LOGO_H_PT * PT_TO_MM
BOX1_W = BOX1_W_PT * PT_TO_MM
BOX_H = TITLE_H_PT * PT_TO_MM

# إضافة ارتفاعات الصفوف
ROW_HEIGHT_TABLE1 = 10  # ارتفاع صفوف الجدول الأول
ROW_HEIGHT_HEADER = 14  # ارتفاع صفوف الرأس
# تقسيم ارتفاعات الجداول السفلية لكل جدول على حدة
ROW_HEIGHT_TABLE3_HEADER = 10  # ارتفاع رأس جدول الأستاذ
ROW_HEIGHT_TABLE3_DATA = 8    # ارتفاع صفوف بيانات جدول الأستاذ
ROW_HEIGHT_TABLE4_HEADER = 10  # ارتفاع رأس جدول رئيس المركز
ROW_HEIGHT_TABLE4_DATA = 16    # ارتفاع صفوف بيانات جدول رئيس المركز

# إضافة إعدادات للهوامش (بالملليمتر)
PAGE_MARGIN_TOP = 0.2     # الهامش العلوي للصفحة
PAGE_MARGIN_BOTTOM = 0.2  # الهامش السفلي للصفحة
PAGE_MARGIN_LEFT = 10     # الهامش الأيسر للصفحة
PAGE_MARGIN_RIGHT = 10    # الهامش الأيمن للصفحة

PT_TO_MM = 0.3528
LOGO_W_PT, LOGO_H_PT = 200, 80
BOX1_W_PT, BOX2_W_PT, BOX3_W_PT = 300, 100, 150
TITLE_H_PT = 40
LOGO_W = LOGO_W_PT * PT_TO_MM
LOGO_H = LOGO_H_PT * PT_TO_MM
BOX1_W = BOX1_W_PT * PT_TO_MM
BOX2_W = BOX2_W_PT * PT_TO_MM
BOX3_W = BOX3_W_PT * PT_TO_MM
BOX_H = TITLE_H_PT * PT_TO_MM

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P','mm','A4')
        # تعيين الهوامش
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        # تصحيح تحذير Deprecation Warning بإزالة المعامل uni=True
        self.add_font('Arial', '', os.path.join(fonts_dir, 'arial.ttf'))
        self.add_font('Arial', 'B', os.path.join(fonts_dir, 'arialbd.ttf'))
        self.set_font('Arial', '', 12)
        # تعيين سمك الخط الافتراضي
        self.set_line_width(0.5)

    def ar_text(self, txt: str) -> str:
        """
        تحويل النص العربي ليتم عرضه بشكل صحيح
        إذا كان النص يحتوي على رمز \n سيتم تجاهله وإرجاع نص مباشرة
        لأن fpdf تتعامل مع السطور الجديدة بشكل مختلف
        """
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

    def multi_line_ar_text(self, txt: str, cell_width: float, font_size: int = 12) -> list:
        """
        تقسيم النص إلى سطور لتتناسب مع عرض الخلية

        المعاملات:
            txt: النص المراد تقسيمه
            cell_width: عرض الخلية بالملليمتر
            font_size: حجم الخط

        العوائد:
            قائمة بأسطر النص بعد التقسيم
        """
        lines = []
        # تقسيم النص إلى كلمات
        words = txt.split(' ')
        current_line = ""

        for word in words:
            # تقدير عرض السطر الحالي مع الكلمة المضافة
            test_line = current_line + " " + word if current_line else word
            # تحويل مؤقت للنص العربي لحساب العرض بشكل صحيح
            ar_test_line = self.ar_text(test_line)

            # حساب عرض النص التقريبي (استخدام تقدير بسيط)
            self.set_font('Arial', '', font_size)
            width = self.get_string_width(ar_test_line)

            # إذا تجاوز العرض المسموح، نضيف السطر الحالي ونبدأ بسطر جديد
            if width > cell_width and current_line:
                lines.append(current_line)
                current_line = word
            else:
                current_line = test_line

        # إضافة السطر الأخير إذا كان غير فارغ
        if current_line:
            lines.append(current_line)

        return lines


def fetch_records(db_path: str, filter_criteria=None):
    """
    جلب السجلات من قاعدة البيانات مع تطبيق معايير التصفية المحددة

    المعاملات:
        db_path: مسار قاعدة البيانات
        filter_criteria: معايير التصفية (اختياري)

    العوائد:
        (logo_path, records): مسار الشعار وقائمة السجلات
    """
    conn = sqlite3.connect(db_path)
    cur = conn.cursor()
    # جلب شعار المؤسسة
    cur.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
    logo_row = cur.fetchone()
    logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None

    # إعداد الاستعلام مع مراعاة معايير التصفية
    query = '''
    SELECT رقم_الامتحان, الاسم_الكامل, الرمز, مركز_الامتحان, المستوى, القسم, القاعة, المؤسسة_الأصلية
    FROM امتحانات
    '''

    # إضافة شروط التصفية
    if filter_criteria:
        conditions = []
        params = []

        for key, value in filter_criteria.items():
            if value:
                conditions.append(f"{key} = ?")
                params.append(value)

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

    # إضافة ترتيب السجلات
    query += " ORDER BY CAST(رقم_الامتحان AS INTEGER), CAST(القاعة AS INTEGER)"

    # تنفيذ الاستعلام
    if filter_criteria and params:
        cur.execute(query, params)
    else:
        cur.execute(query)

    cols = [c[0] for c in cur.description]
    recs = [dict(zip(cols, row)) for row in cur.fetchall()]
    conn.close()
    return logo_path, recs


def generate_report(logo_path, records, output_path, report_title=None, sub_title=None, two_per_page=False):
    """
    إنشاء تقرير المترشحين

    المعاملات:
        logo_path: مسار شعار المؤسسة
        records: سجلات المترشحين
        output_path: مسار ملف الخرج
        report_title: عنوان التقرير (اختياري)
        sub_title: العنوان الفرعي (اختياري)
        two_per_page: طباعة استدعائين في صفحة واحدة (اختياري، افتراضيًا False)
    """
    pdf = ArabicPDF()
    margin = 10
    usable_w = pdf.w - 2 * margin

    # إذا لم يتم تحديد عنوان التقرير، نبحث عنه في قاعدة البيانات
    if not report_title:
        try:
            # البحث عن العنوان في جدولة_الامتحان - المصدر الوحيد
            db_path = os.path.join(os.path.dirname(__file__), 'data.db')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT ملاحظات FROM جدولة_الامتحان WHERE id = 1 LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                report_title = result[0]
        except Exception as e:
            print(f"خطأ في استرجاع عنوان التقرير من قاعدة البيانات: {str(e)}")

    # ترتيب السجلات حسب رقم الامتحان
    def get_exam_number(record):
        exam_number = record.get('رقم_الامتحان', '0')
        # التحقق من نوع البيانات
        if isinstance(exam_number, int):
            return exam_number
        elif isinstance(exam_number, str) and exam_number.isdigit():
            return int(exam_number)
        else:
            return 0

    records.sort(key=get_exam_number)

    # تحديد ارتفاع الاستدعاء الواحد (للاستخدام في حالة two_per_page)
    # تعديل: تصغير القيمة لتكون نصف ارتفاع الصفحة تقريباً لضمان ملائمة استدعائين
    page_height = pdf.h - (2 * margin)  # ارتفاع الصفحة المتاح
    single_invitation_height = page_height / 2 - 5  # نصف ارتفاع الصفحة مع هامش صغير
    
    # طباعة استدعائين في كل صفحة إذا كان الخيار مفعلاً
    if two_per_page:
        # معالجة السجلات في أزواج
        for i in range(0, len(records), 2):
            pdf.add_page()
            
            # معالجة السجل الأول (دائماً موجود)
            process_invitation(pdf, records[i], logo_path, report_title, margin, usable_w, 0, is_two_per_page=True)
            
            # معالجة السجل الثاني (إذا كان موجوداً)
            if i + 1 < len(records):
                # الانتقال إلى النصف الثاني من الصفحة
                pdf.set_y(margin + single_invitation_height)  # تعيين موضع البداية للاستدعاء الثاني
                process_invitation(pdf, records[i+1], logo_path, report_title, margin, usable_w, 0, is_two_per_page=True)
    else:
        # الطريقة العادية: استدعاء واحد في كل صفحة
        for rec in records:
            pdf.add_page()
            process_invitation(pdf, rec, logo_path, report_title, margin, usable_w, 0, is_two_per_page=False)

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"تم إنشاء التقرير: {output_path}")
    return output_path


# دالة جديدة لمعالجة استدعاء فردي (يمكن استخدامها للاستدعاء العادي أو الاستدعاء المزدوج)
def process_invitation(pdf, rec, logo_path, report_title, margin, usable_w, y_offset=0, is_two_per_page=False):
    """
    معالجة استدعاء فردي
    
    المعاملات:
        pdf: كائن PDF
        rec: سجل المترشح
        logo_path: مسار الشعار
        report_title: عنوان التقرير
        margin: هامش الصفحة
        usable_w: العرض المتاح للصفحة
        y_offset: إزاحة Y للاستدعاء (للاستدعاء الثاني في الصفحة)
        is_two_per_page: هل يتم عرض استدعائين في الصفحة؟
    """
    # ضبط عوامل التصغير إذا كان يتم عرض استدعائين
    scale_factor = 0.70 if is_two_per_page else 1.0
    
    # ضبط ارتفاع الشعار والخطوط عند استخدام وضع الاستدعائين
    logo_height = LOGO_H * scale_factor
    font_size_title = 12 if is_two_per_page else 14
    font_size_text = 10 if is_two_per_page else 12
    font_size_table = 9 if is_two_per_page else 12
    table_row_height = ROW_HEIGHT_TABLE1 * scale_factor
    
    # بداية موقع Y
    y = pdf.get_y() + y_offset
    
    # إضافة الشعار
    if logo_path and os.path.exists(logo_path):
        x_logo = (pdf.w - LOGO_W * scale_factor) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W * scale_factor, h=logo_height)
        y += logo_height + 2  # تقليل المسافة بعد الشعار مع الاستدعائين
    
    # مربع العنوان الرئيسي
    pdf.set_draw_color(0,0,255)
    pdf.set_line_width(0.4)
    
    # تحديد ارتفاع موحد للمربع العلوي
    box_height = 12 if is_two_per_page else 14
    
    # استخدام عنوان التقرير المخصص إذا كان متاحاً
    title_text = report_title if report_title else 'محضر بيانات الامتحانات'
    
    # تحديد موقع مربع العنوان في وسط الصفحة
    x = margin
    w = usable_w =195
    
    pdf.set_xy(x, y)
    pdf.set_font('Arial', 'B', font_size_title)
    
    # تقسيم النص إلى سطرين إذا كان طويلاً
    lines = pdf.multi_line_ar_text(title_text, w - 10, font_size_title)
    
    if len(lines) > 1:
        # رسم خلية فارغة أولاً
        pdf.cell(w, box_height, '', border=1, align='C')
        
        # ثم رسم كل سطر داخلها
        line_height = box_height / len(lines)
        for i, line in enumerate(lines[:2]):
            pdf.set_xy(x, y + i * line_height)
            pdf.cell(w, line_height, pdf.ar_text(line), border=0, align='C')
    else:
        # إذا كان النص قصيراً نرسمه كالمعتاد
        pdf.cell(w, box_height, pdf.ar_text(title_text), border=1, align='C')
    
    pdf.set_font('Arial', '', font_size_text)
    y += box_height + 2  # تقليل المسافة بعد العنوان
    
    # الجدول الأول: 4 أعمدة، 4 صفوف (معكوسة الترتيب)
    cols1 = [c * scale_factor for c in COL_WIDTHS_TABLE1]  # تصغير عرض الأعمدة
    
    # محاولة الحصول على قيمة المؤسسة الأصلية
    institution_value = rec.get('المؤسسة_الأصلية', '')
    if not institution_value:
        institution_value = rec.get('المؤسسة-الأصلية', '')
    
    table1_data = [
        [rec.get('الاسم_الكامل',''), 'الاسم الكامل', rec.get('مركز_الامتحان',''), 'مركز الامتحان'],
        [rec.get('الرمز',''), 'رقم المترشح (مسار)', institution_value, 'المؤسسة الأصلية'],
        [rec.get('المستوى',''), 'المستوى', rec.get('القاعة',''), 'قاعة الامتحان'],
        [rec.get('القسم',''), 'القسم', rec.get('رقم_الامتحان',''), 'رقم الامتحان']
    ]
    
    pdf.set_font('Arial', 'B', font_size_table)
    pdf.set_fill_color(230,230,230)
    
    # رسم جدول البيانات بحجم مصغر
    for row_idx, row_data in enumerate(table1_data):
        x = margin
        for col_idx, cell in enumerate(row_data):
            is_header = col_idx == 1 or col_idx == 3
            pdf.set_xy(x, y)
            pdf.cell(cols1[col_idx], table_row_height, pdf.ar_text(cell),
                    border=1, align='C', fill=is_header)
            x += cols1[col_idx]
        y += table_row_height
    
    # تخطي جدول الامتحانات إذا كان في وضع استدعائين في الصفحة
    if not is_two_per_page:
        # الجدول الثاني: جدول الامتحانات
        y += 2
        
        # إضافة جدول جديد مع التحكم الكامل في الأبعاد
        # تعريف عرض الأعمدة للجدول الجديد (5 أعمدة)
        new_table_cols = [40, 40, 40, 40, 35]  # مجموع العرض 195 مم

        # تعريف ارتفاع الصفوف للجدول الجديد
        row_heights = [13, 13, 13, 13, 13, 13, 13]  # ارتفاع كل صف 13 مم

        # عناوين الأعمدة للجدول الجديد
        headers = ['الحصة الرابعة', 'الحصة الثالثة', 'الحصة الثانية', 'الحصة الأولى', 'اليوم']

        # تغيير الخط إلى Calibri حجم 12 أسود غامق
        try:
            # محاولة إضافة الخط إذا لم يكن موجوداً بالفعل
            calibri_b_path = os.path.join(os.path.dirname(__file__), 'fonts', 'calibrib.ttf')
            calibri_path = os.path.join(os.path.dirname(__file__), 'fonts', 'calibri.ttf')

            # التحقق من وجود الخطوط في قائمة الخطوط المضافة
            if 'calibriB' not in pdf.fonts:
                pdf.add_font('Calibri', 'B', calibri_b_path)
            if 'calibri' not in pdf.fonts:
                pdf.add_font('Calibri', '', calibri_path)
        except Exception as e:
            print(f"تحذير: تعذر إضافة خط Calibri: {str(e)}")

        # استخراج بيانات جدول الامتحان من المصدر الوحيد: جدولة_الامتحان
        exam_schedule_data = []
        first_record_day_date = ("", "")  # (اليوم، التاريخ) للسجل الأول

        try:
            db_path = os.path.join(os.path.dirname(__file__), 'data.db')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # الحصول على السنة الدراسية الحالية والأسدس
            cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            academic_year = result[0] if result and result[0] else ""
            semester = result[1] if result and result[1] else ""

            # استعلام عن السجل الأول (id=1) من المصدر الوحيد: جدولة_الامتحان
            cursor.execute("""
                SELECT اليوم, التاريخ
                FROM جدولة_الامتحان
                WHERE id = 1
            """)

            first_record = cursor.fetchone()
            if first_record:
                first_record_day_date = (first_record[0] or "", first_record[1] or "")

            # استعلام عن جدولة الامتحان من المصدر الوحيد للسنة الدراسية والأسدس الحاليين
            cursor.execute("""
                SELECT اليوم, التاريخ,
                       الحصة1, التوقيت1,
                       الحصة2, التوقيت2,
                       الحصة3, التوقيت3,
                       الحصة4, التوقيت4
                FROM جدولة_الامتحان
                WHERE السنة_الدراسية = ? AND الأسدس = ?
                ORDER BY id
            """, (academic_year, semester))

            exam_schedule_data = cursor.fetchall()
            conn.close()
        except Exception as e:
            print(f"خطأ في استرجاع بيانات جدولة الامتحان من المصدر الوحيد: {str(e)}")

        # دمج العمود الأول والثاني معًا والعمود الثالث والرابع معًا في رأس الجدول
        pdf.set_font('Calibri', 'B', 11)  # تغيير الخط إلى Calibri غامق حجم 13
        pdf.set_text_color(0, 0, 0)  # لون أسود للنص
        pdf.set_fill_color(200, 200, 200)  # لون رمادي فاتح لخلفية العناوين

        # عرض الخلايا المدمجة
        col1_2_width = new_table_cols[0] + new_table_cols[1]  # عرض العمود الأول + الثاني
        col3_4_width = new_table_cols[2] + new_table_cols[3]  # عرض العمود الثالث + الرابع

        x = margin
        # 1. رسم الخلية المدمجة الأولى (العمود 1+2)
        pdf.set_xy(x, y)
        pdf.cell(col1_2_width, row_heights[0], pdf.ar_text('بعد الزوال'), border=1, align='C', fill=True)
        x += col1_2_width

        # 2. رسم الخلية المدمجة الثانية (العمود 3+4)
        pdf.set_xy(x, y)
        pdf.cell(col3_4_width, row_heights[0], pdf.ar_text('صباحا '), border=1, align='C', fill=True)
        x += col3_4_width

        # 3. رسم الخلية الخامسة (بدون دمج)
        pdf.set_xy(x, y)
        # تعديل: إلغاء دمج الصف الأول والثاني للعمود الخامس وتقسيمه إلى صفين
        pdf.cell(new_table_cols[4], row_heights[0], pdf.ar_text('اليوم'), border=1, align='C', fill=True)

        # الانتقال إلى الصف التالي
        y += row_heights[0]

        # رسم صف العناوين الفرعية - مع تجاهل العمود الخامس لأنه تم دمجه
        pdf.set_font('Calibri', 'B', 12)  # تغيير الخط إلى Calibri غامق حجم 13
        x = margin
        for i, header in enumerate(headers[:4]):  # فقط أول 4 عناوين
            pdf.set_xy(x, y)
            pdf.cell(new_table_cols[i], row_heights[0], pdf.ar_text(header), border=1, align='C', fill=True)
            x += new_table_cols[i]

        # إضافة العمود الخامس في الصف الثاني
        pdf.set_xy(margin + col1_2_width + col3_4_width, y)
        pdf.cell(new_table_cols[4], row_heights[0], pdf.ar_text('التاريخ'), border=1, align='C', fill=True)

        y += row_heights[0]

        # رسم صفوف الجدول الجديد مع بيانات من جدول جدولة_الامتحان
        pdf.set_font('Calibri', 'B', 11)  # تغيير الخط إلى Calibri غامق حجم 13
        pdf.set_fill_color(255, 255, 255)  # لون أبيض للخلفية

        # تحديد عدد الصفوف المطلوبة (بحد أقصى 3 صفوف)
        max_rows = min(3, len(exam_schedule_data))

        # رسم صفوف البيانات
        for row_idx in range(max_rows):
            # الحصول على بيانات الصف الحالي
            if row_idx < len(exam_schedule_data):
                row_data = exam_schedule_data[row_idx]

                # تحديد اليوم والتاريخ حسب الصف
                if row_idx == 2:  # الصف الثالث (index=2)
                    # استخدام اليوم والتاريخ من السجل الأول (id=1)
                    day = first_record_day_date[0]  # اليوم من السجل الأول
                    date = first_record_day_date[1]  # التاريخ من السجل الأول
                else:
                    day = row_data[0] or ""  # اليوم
                    date = row_data[1] or ""  # التاريخ

                # بيانات الحصص
                subjects = [
                    (row_data[2] or "", row_data[3] or ""),  # الحصة1 والتوقيت1
                    (row_data[4] or "", row_data[5] or ""),  # الحصة2 والتوقيت2
                    (row_data[6] or "", row_data[7] or ""),  # الحصة3 والتوقيت3
                    (row_data[8] or "", row_data[9] or "")   # الحصة4 والتوقيت4
                ]

                # رسم خلايا الحصص (من اليمين إلى اليسار)
                x = margin
                for col_idx in range(4):
                    subject, time = subjects[3 - col_idx]  # عكس الترتيب (من 3 إلى 0)
                    cell_text = f"{subject}\n({time})" if subject and time else ""

                    pdf.set_xy(x, y)

                    # تقسيم النص إلى سطرين إذا كان طويلاً
                    if "\n" in cell_text:
                        # رسم خلية فارغة أولاً
                        pdf.cell(new_table_cols[col_idx], row_heights[row_idx+1], '', border=1, align='C')

                        # ثم رسم كل سطر داخلها
                        parts = cell_text.split("\n")
                        line_height = row_heights[row_idx+1] / 2

                        # السطر الأول (المادة)
                        pdf.set_xy(x, y)
                        pdf.cell(new_table_cols[col_idx], line_height, pdf.ar_text(parts[0]), border=0, align='C')

                        # السطر الثاني (التوقيت) - خط خاص Calibri 8 أسود غامق
                        pdf.set_font('Calibri', 'B', 8)  # خط Calibri حجم 8 أسود غامق للتوقيتات
                        pdf.set_xy(x, y + line_height)
                        pdf.cell(new_table_cols[col_idx], line_height, pdf.ar_text(parts[1]), border=0, align='C')
                        pdf.set_font('Calibri', '', 10)  # إعادة تعيين الخط الافتراضي
                    else:
                        # إذا كان النص قصيراً نرسمه كالمعتاد
                        pdf.cell(new_table_cols[col_idx], row_heights[row_idx+1], pdf.ar_text(cell_text), border=1, align='C')

                    x += new_table_cols[col_idx]

                # رسم خلية اليوم والتاريخ
                pdf.set_xy(x, y)
                day_date_text = f"{day}\n{date}" if day and date else day or date

                if "\n" in day_date_text:
                    # رسم خلية فارغة أولاً
                    pdf.cell(new_table_cols[4], row_heights[row_idx+1], '', border=1, align='C')

                    # ثم رسم كل سطر داخلها
                    parts = day_date_text.split("\n")
                    line_height = row_heights[row_idx+1] / 2

                    # السطر الأول (اليوم)
                    pdf.set_xy(x, y)
                    pdf.cell(new_table_cols[4], line_height, pdf.ar_text(parts[0]), border=0, align='C')

                    # السطر الثاني (التاريخ)
                    pdf.set_xy(x, y + line_height)
                    pdf.cell(new_table_cols[4], line_height, pdf.ar_text(parts[1]), border=0, align='C')
                else:
                    # إذا كان النص قصيراً نرسمه كالمعتاد
                    pdf.cell(new_table_cols[4], row_heights[row_idx+1], pdf.ar_text(day_date_text), border=1, align='C')
            else:
                # رسم صفوف فارغة إذا لم تكن هناك بيانات كافية
                x = margin
                for col_idx in range(5):
                    pdf.set_xy(x, y)
                    pdf.cell(new_table_cols[col_idx], row_heights[row_idx+1], '', border=1, align='C')
                    x += new_table_cols[col_idx]

            y += row_heights[row_idx+1]  # تحديث موقع Y

        # إضافة صفوف فارغة إضافية إذا كان عدد الصفوف أقل من 3
        for row_idx in range(max_rows, 3):
            x = margin
            for col_idx in range(5):
                # إذا كان هذا هو الصف الثالث والعمود الخامس، نعرض بيانات السجل الأول
                if row_idx == 2 and col_idx == 4:
                    day = first_record_day_date[0]  # اليوم من السجل الأول
                    date = first_record_day_date[1]  # التاريخ من السجل الأول
                    day_date_text = f"{day}\n{date}" if day and date else day or date

                    if "\n" in day_date_text:
                        # رسم خلية فارغة أولاً
                        pdf.set_xy(x, y)
                        pdf.cell(new_table_cols[col_idx], row_heights[row_idx+1], '', border=1, align='C')

                        # ثم رسم كل سطر داخلها
                        parts = day_date_text.split("\n")
                        line_height = row_heights[row_idx+1] / 2

                        # السطر الأول (اليوم)
                        pdf.set_xy(x, y)
                        pdf.cell(new_table_cols[col_idx], line_height, pdf.ar_text(parts[0]), border=0, align='C')

                        # السطر الثاني (التاريخ)
                        pdf.set_xy(x, y + line_height)
                        pdf.cell(new_table_cols[col_idx], line_height, pdf.ar_text(parts[1]), border=0, align='C')
                    else:
                        # إذا كان النص قصيراً نرسمه كالمعتاد
                        pdf.set_xy(x, y)
                        pdf.cell(new_table_cols[col_idx], row_heights[row_idx+1], pdf.ar_text(day_date_text), border=1, align='C')
                else:
                    # رسم خلية فارغة
                    pdf.set_xy(x, y)
                    pdf.cell(new_table_cols[col_idx], row_heights[row_idx+1], '', border=1, align='C')

                x += new_table_cols[col_idx]

            y += row_heights[row_idx+1]  # تحديث موقع Y

        # بعد الانتهاء من رسم الجدول الثاني، أضف مساحة ثم أضف مربع النص
        y += 2  # مسافة بعد الجدول الثاني

        # إضافة مسافة بعد الجدول
        y += 5

        # عرض التوجيهات العامة للمترشح باستخدام الصورة مع محاذاة محسنة إلى اليمين

        # تحديد مسار صورة التوجيهات
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        app_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
        images_folder = os.path.join(app_folder, "صور التوجيهات")
        image_path = os.path.join(images_folder, "توجيهات_المترشح.png")

        # التحقق من وجود الصورة
        if os.path.exists(image_path):
            # عرض العنوان أولاً محاذي إلى اليمين
            pdf.set_font('Calibri', 'B', 14)
            pdf.set_xy(margin, y)
            pdf.cell(usable_w, 10, pdf.ar_text("توجيهات عامة للمترشح:"), 0, 0, 'R')
            y += 12

            # تحسين عرض صورة التوجيهات مع هوامش محددة
            margin_left = 0.9  # هامش من اليسار 0.9 نقطة
            margin_right = 0.9  # هامش من اليمين 0.9 نقطة

            # حساب عرض الصورة (العرض الكامل ناقص الهوامش)
            image_width = pdf.w - margin_left - margin_right
            image_height = 80  # ارتفاع الصورة بالملليمتر

            # حساب موضع X للصورة (بدءاً من الهامش الأيسر)
            image_x = margin_left

            # إضافة الصورة بدون إطار
            pdf.image(image_path, x=image_x, y=y, w=image_width, h=image_height)

            # تحديث موقع Y بعد إضافة الصورة
            y += image_height + 5
        else:
            # إذا لم تكن الصورة موجودة، عرض النص من قاعدة البيانات
            pdf.set_font('Calibri', 'B', 14)
            pdf.set_xy(margin, y)
            pdf.cell(usable_w, 10, pdf.ar_text("توجيهات عامة للمترشح:"), 0, 0, 'R')
            y += 12

            # استرجاع ملاحظات من جدولة_الامتحان - المصدر الوحيد
            try:
                db_path = os.path.join(os.path.dirname(__file__), 'data.db')
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()

                # استعلام عن الملاحظات من المصدر الوحيد
                cursor.execute("SELECT ملاحظات FROM جدولة_الامتحان WHERE id = 1")
                result = cursor.fetchone()

                if result and result[0]:
                    # عرض النص مع محاذاة محسنة إلى اليمين
                    pdf.set_font('Calibri', '', 12)

                    # تقسيم النص إلى أسطر متعددة إذا كان طويلاً
                    notes_text = result[0]
                    max_width = usable_w - 10  # ترك هامش صغير

                    # تقسيم النص إلى أسطر
                    lines = pdf.multi_line_ar_text(notes_text, max_width, 12)

                    # عرض كل سطر مع محاذاة يمين
                    for i, line in enumerate(lines):
                        pdf.set_xy(margin, y + (i * 8))
                        pdf.cell(usable_w, 8, pdf.ar_text(line), 0, 0, 'R')

                    y += len(lines) * 8 + 5  # تحديث موقع Y بناءً على عدد الأسطر
                else:
                    # عرض رسالة افتراضية مع محاذاة يمين
                    pdf.set_font('Calibri', '', 12)
                    pdf.set_xy(margin, y)
                    pdf.cell(usable_w, 8, pdf.ar_text("يرجى مراجعة التوجيهات العامة للمترشح."), 0, 0, 'R')
                    y += 10

                conn.close()
            except Exception as e:
                print(f"خطأ في استرجاع الملاحظات: {str(e)}")
                # عرض رسالة افتراضية في حالة الخطأ
                pdf.set_font('Calibri', '', 12)
                pdf.set_xy(margin, y)
                pdf.cell(usable_w, 8, pdf.ar_text("يرجى مراجعة التوجيهات العامة للمترشح."), 0, 0, 'R')
                y += 10

    # رسم خط فاصل بين الاستدعائين في حالة طباعة استدعائين في صفحة واحدة
    if is_two_per_page and y_offset == 0:
        pdf.set_draw_color(128, 128, 128)  # لون رمادي للخط الفاصل
        pdf.set_line_width(0.3)
        pdf.line(margin, y + 5, pdf.w - margin, y + 5)


def print_exams_report(parent=None, level=None, report_title=None, sub_title=None, filter_criteria=None, output_dir=None, two_per_page=False):
    """
    دالة لإنشاء محضر توقيعات المترشحين، يمكن استدعاؤها من واجهات PyQt5

    المعاملات:
        parent: كائن النافذة الأم (لعرض رسائل)
        level: المستوى لتصفية البيانات (اختياري)
        report_title: عنوان المحضر (اختياري)
        sub_title: العنوان الفرعي (اختياري)
        filter_criteria: معايير التصفية الإضافية (اختياري)
        output_dir: مجلد حفظ التقرير (اختياري)
        two_per_page: طباعة استدعائين في صفحة واحدة (اختياري، افتراضيًا False)

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')

        # إعداد معايير التصفية
        criteria = filter_criteria or {}

        # تحديد مجلد حفظ التقرير
        if output_dir and os.path.exists(output_dir):
            reports_dir = output_dir
        else:
            # إنشاء مجلد التقارير الافتراضي إذا لم يتم تحديد مجلد
            reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الامتحانات')

        # التأكد من وجود المجلد
        os.makedirs(reports_dir, exist_ok=True)

        # إضافة المستوى لمعايير التصفية إذا تم تحديده
        if level:
            criteria['المستوى'] = level

        # جلب السجلات
        logo_path, records = fetch_records(db_path, criteria)

        # التحقق من وجود سجلات
        if not records:
            return False, None, "لم يتم العثور على سجلات مطابقة."

        # تحديد اسم الملف بناءً على المعايير
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # إضافة لاحقة للاسم بناءً على معايير التصفية
        suffix_parts = []

        # إضافة المستوى إذا تم تحديده
        if level:
            suffix_parts.append(f"{level}")

        # إضافة المؤسسة الأصلية إذا تم تحديدها
        if criteria.get('المؤسسة_الأصلية'):
            suffix_parts.append(f"{criteria['المؤسسة_الأصلية']}")

        # إضافة مؤشر لطباعة استدعائين في الصفحة
        if two_per_page:
            suffix_parts.append("استدعائين")

        # إنشاء لاحقة الاسم
        file_suffix = "_".join(suffix_parts)
        if file_suffix:
            file_suffix = f"_{file_suffix}"

        # إنشاء اسم الملف
        output_path = os.path.join(reports_dir, f"استدعاءات_المترشحين{file_suffix}_{timestamp}.pdf")

        # إنشاء التقرير مع تمرير خاصية two_per_page
        generate_report(logo_path, records, output_path, report_title, sub_title, two_per_page)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            else:
                # استخدام طريقة أكثر توافقية لفتح الملفات
                import subprocess
                if sys.platform == 'darwin':  # macOS
                    subprocess.call(['open', output_path])
                else:  # Linux
                    subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء التقرير بنجاح ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء التقرير بنجاح."
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ: {str(e)}"


if __name__=='__main__':
    try:
        # اختبار إنشاء تقرير
        db = os.path.join(os.path.dirname(__file__), 'data.db')
        logo, recs = fetch_records(db)

        if not recs:
            print("لم يتم العثور على سجلات في قاعدة البيانات.")
            sys.exit(1)

        # الحصول على عنوان التقرير من المصدر الوحيد: جدولة_الامتحان
        report_title = None
        try:
            conn = sqlite3.connect(db)
            cursor = conn.cursor()
            cursor.execute("SELECT ملاحظات FROM جدولة_الامتحان WHERE id = 1 LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                report_title = result[0]
        except Exception as e:
            print(f"خطأ في استرجاع عنوان التقرير: {str(e)}")

        out = os.path.join(os.path.expanduser('~'),'Desktop','تقارير الامتحانات', f"نموذج_محضر_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
        generate_report(logo, recs, out, report_title)
        print("تم إنشاء التقرير بنجاح.")
    except Exception as e:
        print(f"خطأ: {str(e)}")
        traceback.print_exc()
