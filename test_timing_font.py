#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار خط التوقيتات في جدولة الامتحان
===================================
"""

import os
import sys
import sqlite3
from datetime import datetime

def test_timing_font():
    """اختبار خط التوقيتات الجديد"""
    
    print("🔍 اختبار خط التوقيتات في جدولة الامتحان...")
    print("=" * 60)
    
    try:
        # استيراد ملف print12
        import print12
        print("✅ تم استيراد ملف print12.py بنجاح")
        
        # التحقق من وجود قاعدة البيانات
        db_path = "data.db"
        if not os.path.exists(db_path):
            print("❌ ملف قاعدة البيانات غير موجود!")
            return False
        
        print("✅ ملف قاعدة البيانات موجود")
        
        # التحقق من وجود بيانات جدولة الامتحان
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # فحص بيانات جدولة الامتحان
            cursor.execute("SELECT COUNT(*) FROM جدولة_الامتحان")
            schedule_count = cursor.fetchone()[0]
            
            print(f"📊 عدد سجلات جدولة الامتحان: {schedule_count}")
            
            if schedule_count > 0:
                # فحص وجود توقيتات
                cursor.execute("""
                    SELECT التوقيت1, التوقيت2, التوقيت3, التوقيت4 
                    FROM جدولة_الامتحان 
                    WHERE التوقيت1 IS NOT NULL OR التوقيت2 IS NOT NULL 
                       OR التوقيت3 IS NOT NULL OR التوقيت4 IS NOT NULL
                    LIMIT 1
                """)
                timing_result = cursor.fetchone()
                
                if timing_result:
                    timings = [t for t in timing_result if t and t.strip()]
                    if timings:
                        print(f"✅ توجد توقيتات في البيانات: {', '.join(timings[:2])}...")
                    else:
                        print("⚠️ لا توجد توقيتات في البيانات")
                else:
                    print("⚠️ لا توجد توقيتات في البيانات")
            
            conn.close()
        except Exception as e:
            print(f"❌ خطأ في فحص بيانات جدولة الامتحان: {e}")
            return False
        
        # إنشاء تقرير اختبار
        print("\n🧪 إنشاء تقرير اختبار للتحقق من خط التوقيتات...")
        
        test_reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'اختبار_خط_التوقيتات')
        os.makedirs(test_reports_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        success, output_path, message = print12.print_exams_report(
            parent=None,
            report_title="اختبار خط التوقيتات - Calibri 8 أسود غامق",
            output_dir=test_reports_dir,
            two_per_page=False
        )
        
        if success:
            print(f"✅ تم إنشاء التقرير بنجاح!")
            print(f"📁 مسار الملف: {output_path}")
            
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"📊 حجم الملف: {file_size} بايت")
                
                print("\n🎯 التحقق المطلوب:")
                print("1. افتح الملف المُنشأ")
                print("2. ابحث عن جدولة الامتحان")
                print("3. تأكد من أن التوقيتات تظهر بخط:")
                print("   • نوع الخط: Calibri")
                print("   • حجم الخط: 8")
                print("   • نمط الخط: أسود غامق (Bold)")
                print("4. قارن مع خط المواد (يجب أن يكون مختلف)")
                
                print("\n✨ التحسين المطبق:")
                print("   📝 خط التوقيتات: Calibri 8 Bold")
                print("   📝 خط المواد: Calibri 10 عادي")
                print("   🎯 التوقيتات أكثر وضوحاً ومميزة")
                
                return True
            else:
                print("❌ لم يتم إنشاء الملف")
                return False
        else:
            print(f"❌ فشل في إنشاء التقرير: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def create_sample_schedule_data():
    """إنشاء بيانات جدولة عينة للاختبار"""
    
    print("\n📝 إنشاء بيانات جدولة عينة...")
    print("-" * 50)
    
    try:
        db_path = "data.db"
        if not os.path.exists(db_path):
            print("❌ ملف قاعدة البيانات غير موجود")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إنشاء بيانات عينة للجدولة
        sample_data = [
            {
                'id': 1,
                'اليوم': 'الاثنين',
                'التاريخ': '2024-01-15',
                'الحصة1': 'الرياضيات',
                'التوقيت1': '08:00 - 10:00',
                'الحصة2': 'الفيزياء',
                'التوقيت2': '10:30 - 12:30',
                'الحصة3': 'الكيمياء',
                'التوقيت3': '14:00 - 16:00',
                'الحصة4': '',
                'التوقيت4': ''
            },
            {
                'id': 2,
                'اليوم': 'الثلاثاء',
                'التاريخ': '2024-01-16',
                'الحصة1': 'اللغة العربية',
                'التوقيت1': '08:00 - 10:00',
                'الحصة2': 'اللغة الفرنسية',
                'التوقيت2': '10:30 - 12:30',
                'الحصة3': 'التاريخ والجغرافيا',
                'التوقيت3': '14:00 - 16:00',
                'الحصة4': 'التربية الإسلامية',
                'التوقيت4': '16:30 - 18:30'
            }
        ]
        
        # إدراج أو تحديث البيانات
        for data in sample_data:
            cursor.execute("""
                INSERT OR REPLACE INTO جدولة_الامتحان 
                (id, اليوم, التاريخ, الحصة1, التوقيت1, الحصة2, التوقيت2, 
                 الحصة3, التوقيت3, الحصة4, التوقيت4, السنة_الدراسية, الأسدس)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, '2023-2024', 'الأول')
            """, (
                data['id'], data['اليوم'], data['التاريخ'],
                data['الحصة1'], data['التوقيت1'],
                data['الحصة2'], data['التوقيت2'],
                data['الحصة3'], data['التوقيت3'],
                data['الحصة4'], data['التوقيت4']
            ))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء بيانات جدولة الامتحان العينة")
        print("💡 البيانات تحتوي على توقيتات متنوعة لاختبار الخط الجديد")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء بيانات الجدولة العينة: {e}")
        return False

def show_font_specifications():
    """عرض مواصفات الخط الجديد"""
    
    print("\n📋 مواصفات خط التوقيتات الجديد:")
    print("-" * 50)
    
    print("🔤 نوع الخط: Calibri")
    print("📏 حجم الخط: 8 نقاط")
    print("💪 نمط الخط: Bold (غامق)")
    print("🎨 لون الخط: أسود")
    
    print("\n📊 مقارنة مع الخطوط الأخرى:")
    print("   • المواد: Calibri 10 عادي")
    print("   • التوقيتات: Calibri 8 غامق ✨")
    print("   • العناوين: Calibri 14 غامق")
    
    print("\n🎯 الفوائد:")
    print("   ✅ التوقيتات أكثر وضوحاً")
    print("   ✅ سهولة التمييز بين المواد والتوقيتات")
    print("   ✅ مظهر احترافي ومنظم")
    print("   ✅ قابلية قراءة محسنة")

if __name__ == "__main__":
    print("🚀 بدء اختبار خط التوقيتات في جدولة الامتحان")
    print("=" * 60)
    
    # إنشاء بيانات عينة
    sample_created = create_sample_schedule_data()
    
    if sample_created:
        # تشغيل الاختبار
        test_result = test_timing_font()
        
        # عرض مواصفات الخط
        show_font_specifications()
        
        print("\n" + "=" * 60)
        print("📋 نتيجة الاختبار:")
        
        if test_result:
            print("🎉 نجح الاختبار!")
            print("✨ تم تطبيق خط التوقيتات الجديد:")
            print("   📝 Calibri 8 Bold للتوقيتات")
            print("   🎯 التوقيتات أكثر وضوحاً ومميزة")
            print("📖 افتح الملف المُنشأ للتحقق البصري")
        else:
            print("⚠️ فشل الاختبار")
            print("🔧 تحقق من وجود البيانات والملفات")
    else:
        print("\n⚠️ لا يمكن إجراء الاختبار بدون بيانات جدولة الامتحان")
        print("📝 تحقق من قاعدة البيانات وأعد المحاولة")
    
    print("=" * 60)
