#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار سريع لمكتبات PDF
"""

def test_imports():
    """اختبار استيراد المكتبات"""
    try:
        print("🔍 اختبار استيراد المكتبات...")
        
        # اختبار reportlab
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.pdfgen import canvas
            print("✅ reportlab متوفر")
        except ImportError as e:
            print(f"❌ reportlab غير متوفر: {e}")
            return False
        
        # اختبار PyPDF2
        try:
            from PyPDF2 import PdfWriter, PdfReader
            print("✅ PyPDF2 متوفر")
        except ImportError as e:
            print(f"❌ PyPDF2 غير متوفر: {e}")
            return False
        
        # اختبار arabic_reshaper
        try:
            import arabic_reshaper
            from bidi.algorithm import get_display
            print("✅ arabic_reshaper متوفر")
        except ImportError as e:
            print(f"⚠️ arabic_reshaper غير متوفر: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def create_simple_pdf():
    """إنشاء PDF بسيط"""
    try:
        import os
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        output_path = os.path.join(desktop_path, "اختبار_بسيط.pdf")
        
        c = canvas.Canvas(output_path, pagesize=A4)
        width, height = A4
        
        # إضافة نص بسيط
        c.setFont("Helvetica", 16)
        c.drawString(100, height - 100, "Test PDF Created Successfully!")
        c.drawString(100, height - 150, "PDF تم إنشاؤه بنجاح!")
        
        c.save()
        
        print(f"✅ تم إنشاء PDF بسيط: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء PDF: {e}")
        return None

if __name__ == "__main__":
    print("🚀 بدء الاختبار السريع...")
    
    if test_imports():
        print("📄 إنشاء PDF تجريبي...")
        pdf_path = create_simple_pdf()
        if pdf_path:
            print("🎉 تم الاختبار بنجاح!")
        else:
            print("❌ فشل في إنشاء PDF")
    else:
        print("❌ فشل في اختبار المكتبات")
