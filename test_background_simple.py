#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار بسيط لميزة إضافة خلفية PDF
"""

import os
from datetime import datetime

def create_simple_background():
    """إنشاء خلفية PDF بسيطة للاختبار"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        
        # تحديد مسار الحفظ
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        output_path = os.path.join(desktop_path, "خلفية_بسيطة.pdf")
        
        # إنشاء ملف PDF
        c = canvas.Canvas(output_path, pagesize=A4)
        width, height = A4
        
        # إضافة خلفية ملونة
        c.setFillColorRGB(0.95, 0.98, 1.0)  # أزرق فاتح جداً
        c.rect(0, 0, width, height, fill=1)
        
        # إضافة شبكة خفيفة
        c.setStrokeColorRGB(0.8, 0.8, 0.8)
        c.setLineWidth(0.5)
        
        # خطوط عمودية
        for x in range(0, int(width), 50):
            c.line(x, 0, x, height)
        
        # خطوط أفقية
        for y in range(0, int(height), 50):
            c.line(0, y, width, y)
        
        # إضافة نص في الزوايا
        c.setFillColorRGB(0.7, 0.7, 0.7)
        c.setFont("Helvetica", 12)
        c.drawString(50, height - 50, "خلفية تجريبية")
        c.drawString(50, 50, f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d')}")
        
        # إضافة شعار وهمي في الوسط
        c.setFillColorRGB(0.9, 0.9, 0.9)
        c.setFont("Helvetica", 48)
        c.drawCentredText(width/2, height/2, "🏫")
        
        # إضافة إطار خارجي
        c.setStrokeColorRGB(0.5, 0.5, 0.5)
        c.setLineWidth(2)
        c.rect(20, 20, width - 40, height - 40)
        
        # حفظ الملف
        c.save()
        
        print(f"✅ تم إنشاء خلفية بسيطة: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الخلفية: {e}")
        return None

def test_background_merge():
    """اختبار دمج الخلفية"""
    try:
        from PyPDF2 import PdfWriter, PdfReader
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        
        print("🧪 بدء اختبار دمج الخلفية...")
        
        # إنشاء خلفية
        background_path = create_simple_background()
        if not background_path:
            return False
        
        # إنشاء محتوى للدمج
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        content_path = os.path.join(desktop_path, "محتوى_تجريبي.pdf")
        
        c = canvas.Canvas(content_path, pagesize=A4)
        width, height = A4
        
        # إضافة نص واضح
        c.setFillColorRGB(0, 0, 0)
        c.setFont("Helvetica-Bold", 24)
        c.drawCentredText(width/2, height - 100, "تقرير تجريبي")
        
        c.setFont("Helvetica", 16)
        c.drawCentredText(width/2, height - 200, "هذا النص يجب أن يظهر فوق الخلفية")
        
        # إضافة مربع ملون
        c.setFillColorRGB(1, 1, 0, alpha=0.7)  # أصفر شفاف
        c.rect(100, height - 400, width - 200, 100, fill=1)
        
        c.setFillColorRGB(0, 0, 0)
        c.drawCentredText(width/2, height - 350, "مربع ملون فوق الخلفية")
        
        c.save()
        
        # دمج الخلفية مع المحتوى
        output_path = os.path.join(desktop_path, "تقرير_مع_خلفية.pdf")
        
        with open(background_path, 'rb') as bg_file:
            background_reader = PdfReader(bg_file)
            background_page = background_reader.pages[0]
            
            with open(content_path, 'rb') as content_file:
                content_reader = PdfReader(content_file)
                content_page = content_reader.pages[0]
                
                writer = PdfWriter()
                
                # دمج المحتوى مع الخلفية
                background_page.merge_page(content_page)
                writer.add_page(background_page)
                
                with open(output_path, 'wb') as output_file:
                    writer.write(output_file)
        
        # حذف الملفات المؤقتة
        os.remove(content_path)
        
        print(f"✅ تم إنشاء التقرير مع الخلفية: {output_path}")
        
        # فتح الملف
        try:
            import subprocess
            if os.name == 'nt':  # Windows
                os.startfile(output_path)
            else:
                subprocess.call(['open', output_path])
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدمج: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار ميزة الخلفية...")
    success = test_background_merge()
    if success:
        print("🎉 تم الاختبار بنجاح!")
    else:
        print("❌ فشل الاختبار!")
