#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إنشاء ملف PDF تجريبي ليكون بمثابة خلفية للاختبار
"""

import os

def create_sample_background():
    """إنشاء ملف PDF تجريبي للخلفية"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        import arabic_reshaper
        from bidi.algorithm import get_display
        
        # تحديد مسار الحفظ
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        output_path = os.path.join(desktop_path, "خلفية_تجريبية.pdf")
        
        # إنشاء ملف PDF
        c = canvas.Canvas(output_path, pagesize=A4)
        width, height = A4
        
        # محاولة تحميل خط عربي
        try:
            fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
            if os.path.exists(os.path.join(fonts_dir, 'arial.ttf')):
                pdfmetrics.registerFont(TTFont('Arabic', os.path.join(fonts_dir, 'arial.ttf')))
                font_name = 'Arabic'
            else:
                font_name = 'Helvetica'
        except:
            font_name = 'Helvetica'
        
        # دالة لتحويل النص العربي
        def ar_text(text):
            try:
                reshaped = arabic_reshaper.reshape(str(text))
                return get_display(reshaped)
            except:
                return str(text)
        
        # إضافة خلفية ملونة خفيفة
        c.setFillColorRGB(0.95, 0.98, 1.0)  # أزرق فاتح جداً
        c.rect(0, 0, width, height, fill=1)
        
        # إضافة شبكة خفيفة
        c.setStrokeColorRGB(0.9, 0.9, 0.9)
        c.setLineWidth(0.5)
        
        # خطوط عمودية
        for x in range(50, int(width), 50):
            c.line(x, 0, x, height)
        
        # خطوط أفقية
        for y in range(50, int(height), 50):
            c.line(0, y, width, y)
        
        # إضافة شعار وهمي في الزاوية
        c.setFillColorRGB(0.8, 0.8, 0.8)
        c.setFont(font_name, 24)
        logo_text = ar_text("🏫 المؤسسة التعليمية")
        c.drawRightString(width - 50, height - 50, logo_text)
        
        # إضافة نص في الأسفل
        c.setFillColorRGB(0.7, 0.7, 0.7)
        c.setFont(font_name, 10)
        footer_text = ar_text("خلفية تجريبية - برنامج المعين في الحراسة العامة")
        c.drawCentredText(width/2, 30, footer_text)
        
        # إضافة إطار خارجي
        c.setStrokeColorRGB(0.6, 0.6, 0.6)
        c.setLineWidth(2)
        c.rect(20, 20, width - 40, height - 40)
        
        # حفظ الملف
        c.save()
        
        print(f"✅ تم إنشاء ملف الخلفية التجريبي بنجاح!")
        print(f"📁 مسار الملف: {output_path}")
        
        return output_path
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف الخلفية: {e}")
        return None

if __name__ == "__main__":
    create_sample_background()
