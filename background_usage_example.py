"""
مثال على كيفية استخدام خلفية PDF في التقارير
هذا الملف يوضح كيفية تعديل ملفات الطباعة الموجودة لتستخدم الخلفية
"""

import os
import sys
from background_merger import get_background_path, merge_pdf_with_background

def create_report_with_background_example():
    """مثال على إنشاء تقرير مع خلفية"""
    
    # خطوة 1: إنشاء التقرير العادي (محتوى فقط)
    temp_report_path = "temp_report.pdf"
    
    # هنا يتم إنشاء التقرير العادي باستخدام الكود الموجود
    # مثال: create_normal_report(temp_report_path)
    
    # خطوة 2: الحصول على مسار الخلفية من قاعدة البيانات
    background_path = get_background_path("data.db")
    
    # خطوة 3: تحديد مسار الملف النهائي
    final_report_path = "final_report_with_background.pdf"
    
    # خطوة 4: دمج التقرير مع الخلفية
    success = merge_pdf_with_background(
        content_pdf_path=temp_report_path,
        output_pdf_path=final_report_path,
        background_pdf_path=background_path,
        db_path="data.db"
    )
    
    # خطوة 5: حذف الملف المؤقت
    if os.path.exists(temp_report_path):
        os.remove(temp_report_path)
    
    if success:
        print(f"✅ تم إنشاء التقرير مع الخلفية: {final_report_path}")
        return final_report_path
    else:
        print("❌ فشل في إنشاء التقرير مع الخلفية")
        return None

def modify_existing_print_file_example():
    """
    مثال على كيفية تعديل ملف طباعة موجود لاستخدام الخلفية
    
    التعديل المطلوب في ملفات print*.py:
    """
    
    example_code = '''
# في بداية الملف، أضف:
from background_merger import get_background_path, merge_pdf_with_background

# في الدالة التي تحفظ التقرير، بدلاً من:
# c.save()  # reportlab
# أو بدلاً من:
# with open(output_path, 'wb') as output_file:
#     writer.write(output_file)

# استخدم:
def save_report_with_background(canvas_or_writer, temp_path, final_path):
    """حفظ التقرير مع الخلفية"""
    
    # 1. حفظ التقرير في ملف مؤقت
    if hasattr(canvas_or_writer, 'save'):  # reportlab canvas
        canvas_or_writer.save()
        temp_report_path = temp_path
    else:  # PyPDF2 writer
        with open(temp_path, 'wb') as temp_file:
            canvas_or_writer.write(temp_file)
        temp_report_path = temp_path
    
    # 2. دمج مع الخلفية
    success = merge_pdf_with_background(
        content_pdf_path=temp_report_path,
        output_pdf_path=final_path,
        db_path="data.db"  # تأكد من المسار الصحيح
    )
    
    # 3. حذف الملف المؤقت
    if os.path.exists(temp_report_path) and temp_report_path != final_path:
        os.remove(temp_report_path)
    
    return success

# مثال للاستخدام في دالة إنشاء التقرير:
def create_attendance_report_with_background():
    """مثال على إنشاء تقرير الحضور مع خلفية"""
    
    # إعداد المسارات
    temp_path = "temp_attendance.pdf"  
    final_path = "attendance_report_with_background.pdf"
    
    # إنشاء التقرير العادي
    c = canvas.Canvas(temp_path, pagesize=A4)
    # ... إضافة محتوى التقرير ...
    
    # حفظ مع الخلفية
    success = save_report_with_background(c, temp_path, final_path)
    
    if success:
        print("تم إنشاء تقرير الحضور مع الخلفية")
        return final_path
    else:
        print("فشل في إضافة الخلفية، تم حفظ التقرير العادي")
        c.save()  # حفظ عادي
        return temp_path
    '''
    
    print("مثال على التعديل المطلوب:")
    print(example_code)

if __name__ == "__main__":
    print("🔧 أمثلة على استخدام خلفية PDF في التقارير")
    print("="*60)
    
    # مثال 1: إنشاء تقرير جديد مع خلفية
    print("\n1️⃣ مثال إنشاء تقرير مع خلفية:")
    # create_report_with_background_example()
    
    # مثال 2: كيفية تعديل ملفات الطباعة الموجودة
    print("\n2️⃣ مثال تعديل ملفات الطباعة الموجودة:")
    modify_existing_print_file_example()
    
    print("\n✅ انتهت الأمثلة")
    print("\n📝 للتطبيق:")
    print("1. تأكد من تثبيت المكتبات المطلوبة")
    print("2. استخدم وحدة background_merger في ملفات الطباعة")
    print("3. اتبع المثال أعلاه لتعديل الملفات الموجودة")
