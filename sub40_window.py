"""
نافذة إدارة التقارير واللوائح والاستدعاءات
"""

import os
import sys
import sqlite3
import time
import subprocess
from pathlib import Path
from datetime import datetime
from PyQt5.QtWidgets import (
    QDialog, QApplication, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QMessageBox, QFrame, QTextEdit,
    QProgressDialog, QComboBox, QDialogButtonBox, QFileDialog
)
from PyQt5.QtGui import QFont, QIcon, QPixmap
from PyQt5.QtCore import Qt, QSize

class CustomMessageDialog(QDialog):
    """نافذة رسائل مخصصة بنفس نمط sub100_window.py"""

    def __init__(self, parent=None, title="", message="", icon_type="info"):
        super().__init__(parent)
        self.setWindowTitle(title)

        # تعديل حجم النافذة حسب نوع الرسالة
        if icon_type == "success":
            self.setFixedSize(450, 350)  # حجم نافذة النجاح 450×350
        else:
            self.setFixedSize(450, 250)  # حجم النوافذ الأخرى 450×250

        self.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            self.setWindowIcon(app_icon)

            # تعيين أيقونة البرنامج كأيقونة للنافذة
            if parent and hasattr(parent, 'windowIcon') and not parent.windowIcon().isNull():
                self.setWindowIcon(parent.windowIcon())
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تحديد ألوان النافذة حسب نوع الرسالة
        if icon_type == "warning":
            bg_color = "#fff8f0"
            border_color = "#f39c12"
            title_color = "#d35400"
            button_bg = "#f39c12"
            button_hover_bg = "#e67e22"
        elif icon_type == "error":
            bg_color = "#fff0f0"
            border_color = "#e74c3c"
            title_color = "#c0392b"
            button_bg = "#e74c3c"
            button_hover_bg = "#c0392b"
        elif icon_type == "success":
            bg_color = "#f0fff0"
            border_color = "#2ecc71"
            title_color = "#27ae60"
            button_bg = "#2ecc71"
            button_hover_bg = "#27ae60"
        else:  # info
            bg_color = "#f0f8ff"
            border_color = "#3498db"
            title_color = "#2980b9"
            button_bg = "#3498db"
            button_hover_bg = "#2980b9"

        # تنسيق النافذة
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {bg_color};
                border: 2px solid {border_color};
                border-radius: 10px;
            }}
            QLabel {{
                color: #333333;
                font-weight: bold;
            }}
            QLabel#message_label {{
                background-color: white;
                border: 1px solid {border_color};
                border-radius: 5px;
                padding: 15px;
                font-size: 14pt;
            }}
            QPushButton {{
                background-color: {button_bg};
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {button_hover_bg};
                border: 2px solid {button_bg};
            }}
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة مناسبة
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            # استخدام أيقونة قياسية
            if icon_type == "warning":
                icon = QPixmap(self.style().standardPixmap(self.style().SP_MessageBoxWarning))
            elif icon_type == "error":
                icon = QPixmap(self.style().standardPixmap(self.style().SP_MessageBoxCritical))
            elif icon_type == "success":
                icon = QPixmap(self.style().standardPixmap(self.style().SP_MessageBoxInformation))
            else:  # info
                icon = QPixmap(self.style().standardPixmap(self.style().SP_MessageBoxInformation))

            icon = icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(icon)
        except Exception:
            # استخدام نص بديل في حالة الفشل
            if icon_type == "warning":
                icon_label.setText("⚠️")
            elif icon_type == "error":
                icon_label.setText("❌")
            elif icon_type == "success":
                icon_label.setText("✓")
            else:  # info
                icon_label.setText("ℹ️")
            icon_label.setFont(QFont("Arial", 24))
            icon_label.setStyleSheet(f"color: {border_color};")

        header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet(f"color: {title_color};")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة
        message_label = QLabel(message)
        message_label.setObjectName("message_label")

        # تعديل خط الرسالة حسب نوع الرسالة
        if icon_type == "success":
            message_label.setFont(QFont("Calibri", 13, QFont.Bold))
            message_label.setStyleSheet("color: #000080;")  # أزرق غامق
        else:
            message_label.setFont(QFont("Calibri", 13))

        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة زر الموافقة
        button_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.clicked.connect(self.accept)
        button_layout.addWidget(ok_button)

        layout.addLayout(button_layout)

# دالة مساعدة للحصول على مجلد التنزيلات
def get_downloads_folder():
    """الحصول على مسار مجلد التنزيلات"""
    # محاولة الحصول على مجلد التنزيلات بطريقة متوافقة مع أنظمة التشغيل المختلفة
    try:
        # في ويندوز
        if os.name == 'nt':
            import winreg
            sub_key = r'SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders'
            downloads_guid = '{374DE290-123F-4565-9164-39C4925E467B}'
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, sub_key) as key:
                downloads_folder = winreg.QueryValueEx(key, downloads_guid)[0]
                return downloads_folder

        # في ماك وأنظمة يونكس
        else:
            downloads_folder = os.path.join(os.path.expanduser('~'), 'Downloads')
            if os.path.exists(downloads_folder):
                return downloads_folder
    except Exception:
        pass

    # إذا فشلت الطرق السابقة، استخدم مجلد المستندات
    try:
        documents_folder = os.path.join(os.path.expanduser('~'), 'Documents')
        if os.path.exists(documents_folder):
            downloads_folder = os.path.join(documents_folder, 'Downloads')
            os.makedirs(downloads_folder, exist_ok=True)
            return downloads_folder
    except Exception:
        pass

    # إذا فشلت جميع المحاولات، استخدم سطح المكتب
    desktop_folder = os.path.join(os.path.expanduser('~'), 'Desktop')
    downloads_folder = os.path.join(desktop_folder, 'Downloads')
    os.makedirs(downloads_folder, exist_ok=True)
    return downloads_folder

class Sub40Window(QDialog):
    """نافذة إدارة التقارير واللوائح والاستدعاءات"""

    def __init__(self, parent=None, db_path=None):
        super().__init__(parent)
        self.parent = parent
        self.db_path = db_path or "data.db"

        self.initUI()
        self.loadData()

    def initUI(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة الرئيسية
        self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")
        self.setFixedSize(1000, 800)
        self.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            self.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # إعداد التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # إضافة عنوان النافذة
        title_label = QLabel("إدارة التقارير واللوائح والاستدعاءات")
        title_label.setFont(QFont("Calibri", 20, QFont.Bold))
        title_label.setStyleSheet("color: #2980b9;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # إضافة عنوان فرعي ثابت
        subtitle_label = QLabel("يمكن للمستخدم تغيير وتبديل عناوين التقارير حسب رغبته بشرط أن لا يتجاوز الإطار الخاص بالعنوان")
        subtitle_label.setFont(QFont("Calibri", 12, QFont.Bold))
        subtitle_label.setStyleSheet("color: #27ae60;")
        subtitle_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(subtitle_label)

        # إضافة مسافة بعد العنوان الفرعي
        main_layout.addSpacing(10)

        # إضافة إطار لمربعات النص
        form_frame = QFrame()
        form_frame.setFrameShape(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #f0f8ff;
                border: 1px solid #3498db;
                border-radius: 10px;
            }
        """)

        form_layout = QVBoxLayout(form_frame)
        form_layout.setContentsMargins(20, 20, 20, 20)
        form_layout.setSpacing(15)

        # إضافة مربعات النص مع تسمياتها
        self.text_fields = {}
        field_names = [
            ("العنوان1", "عنوان الاستدعاء:"),
            ("العنوان2", "عنوان لائحة الحضور:"),
            ("العنوان3", "عنوان الإحصائيات:"),
            ("العنوان4", "عنوان جدولة الامتحان:"),
            ("العنوان5", "عنوان: المحضر الجماعي")
        ]

        for field_id, field_label in field_names:
            field_layout = QHBoxLayout()

            label = QLabel(field_label)
            label.setFont(QFont("Calibri", 14, QFont.Bold))
            label.setStyleSheet("color: #2980b9; border: 1px solid #3498db; border-radius: 5px; padding: 5px;")
            label.setFixedWidth(200)
            label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

            text_field = QLineEdit()
            text_field.setFont(QFont("Calibri", 14))
            text_field.setStyleSheet("""
                QLineEdit {
                    border: 1px solid #3498db;
                    border-radius: 5px;
                    padding: 5px;
                    background-color: white;
                    color: black;
                    font-weight: bold;
                }
            """)
            text_field.setFixedSize(600, 40)

            self.text_fields[field_id] = text_field

            field_layout.addWidget(label)
            field_layout.addWidget(text_field)
            field_layout.addStretch()

            form_layout.addLayout(field_layout)

        main_layout.addWidget(form_frame)

        # إضافة إطار للأزرار
        buttons_frame = QFrame()
        buttons_frame.setFrameShape(QFrame.StyledPanel)
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f0f8ff;
                border: 1px solid #3498db;
                border-radius: 10px;
            }
        """)

        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(20, 20, 20, 20)
        buttons_layout.setSpacing(15)

        # إضافة الأزرار في صفين
        button_grid = QGridLayout()
        button_grid.setSpacing(10)

        button_data = [
            ("لوائح الحضور", self.attendance_by_subject, 0, 0, "#2ecc71"),
            ("استدعاءات المترشحين", self.candidate_calls, 0, 1, "#3498db"),
            ("إحصائيات", self.statistics, 0, 2, "#e74c3c"),
            ("لوائح المترشحين", self.attendance_all_subjects, 0, 3, "#f39c12"),
            ("ملصقات الطاولات", self.table_labels, 1, 0, "#9b59b6"),
            ("توجيهات عامة للمترشح (ة)", self.general_instructions, 1, 1, "#95a5a6"),
            ("المحضر الجماعي للمترشحين", self.collective_report, 1, 2, "#1abc9c"),
            ("إضافة خلفية", self.add_background, 1, 3, "#e67e22"),
            ("حفظ", self.save_changes, 2, 0, "#1abc9c")
        ]

        for button_text, button_function, row, col, button_color in button_data:
            button = QPushButton(button_text)
            button.setFont(QFont("Calibri", 12, QFont.Bold))
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {button_color};
                    color: white;
                    border-radius: 5px;
                    padding: 5px;
                }}
                QPushButton:hover {{
                    background-color: {button_color}99;
                    border: 2px solid {button_color};
                }}
            """)
            button.setFixedSize(220, 35)  # تعديل الارتفاع من 30 إلى 35
            button.setCursor(Qt.PointingHandCursor)
            button.clicked.connect(button_function)

            button_grid.addWidget(button, row, col, Qt.AlignCenter)

        buttons_layout.addLayout(button_grid)
        main_layout.addWidget(buttons_frame)

        # إضافة مساحة فارغة في النهاية
        main_layout.addStretch()

    def loadData(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # التحقق من وجود جدول الامتحان وإنشائه إذا لم يكن موجودًا
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود الجدول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_الامتحان'")
            if not cursor.fetchone():
                # إنشاء الجدول إذا لم يكن موجودًا
                cursor.execute("""
                    CREATE TABLE جدول_الامتحان (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        العنوان1 TEXT,
                        العنوان2 TEXT,
                        العنوان3 TEXT,
                        العنوان4 TEXT,
                        العنوان5 TEXT,
                        مسار_خلفية_PDF TEXT
                    )
                """)
                # إضافة سجل فارغ
                cursor.execute("""
                    INSERT INTO جدول_الامتحان (العنوان1, العنوان2, العنوان3, العنوان4, العنوان5, مسار_خلفية_PDF)
                    VALUES ('', '', '', '', '', '')
                """)
                conn.commit()
                self.show_message("معلومات", "تم إنشاء جدول الامتحان بنجاح.", "info")
            else:
                # التحقق من وجود عمود مسار_خلفية_PDF وإضافته إذا لم يكن موجودًا
                cursor.execute("PRAGMA table_info(جدول_الامتحان)")
                columns = [column[1] for column in cursor.fetchall()]
                if 'مسار_خلفية_PDF' not in columns:
                    cursor.execute("ALTER TABLE جدول_الامتحان ADD COLUMN مسار_خلفية_PDF TEXT")
                    conn.commit()

            # التحقق من وجود سجلات في الجدول
            cursor.execute("SELECT COUNT(*) FROM جدول_الامتحان")
            count = cursor.fetchone()[0]

            if count == 0:
                # إضافة سجل فارغ إذا كان الجدول فارغًا
                cursor.execute("""
                    INSERT INTO جدول_الامتحان (العنوان1, العنوان2, العنوان3, العنوان4, العنوان5)
                    VALUES ('', '', '', '', '')
                """)
                conn.commit()

            # تحميل السجل الأول
            cursor.execute("SELECT * FROM جدول_الامتحان LIMIT 1")
            record = cursor.fetchone()

            if record:
                # عرض البيانات في مربعات النص
                self.text_fields["العنوان1"].setText(record[1] or "")
                self.text_fields["العنوان2"].setText(record[2] or "")
                self.text_fields["العنوان3"].setText(record[3] or "")
                self.text_fields["العنوان4"].setText(record[4] or "")
                self.text_fields["العنوان5"].setText(record[5] or "")

                # تحميل مسار خلفية PDF إذا كان موجودًا
                if len(record) > 6 and record[6]:
                    self.background_pdf_path = record[6]
                else:
                    self.background_pdf_path = ""



            conn.close()
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}", "error")

    def show_message(self, title, message, icon_type="info"):
        """عرض رسالة مخصصة"""
        dialog = CustomMessageDialog(self, title, message, icon_type)
        dialog.exec_()

    def attendance_by_subject(self):
        """إنشاء لوائح الحضور"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("لوائح الحضور - إدارة التقارير واللوائح والاستدعاءات")

            # التحقق من إدخال البيانات المطلوبة
            title2 = self.text_fields["العنوان2"].text().strip()

            if not title2:
                self.show_message("تنبيه", "الرجاء إدخال عنوان لائحة الحضور على الأقل.", "warning")
                return

            # إنشاء نافذة حوار لاختيار المادة
            choice_dialog = QDialog(self)
            choice_dialog.setWindowTitle("اختيار المادة")
            choice_dialog.setFixedSize(350, 300)  # حجم النافذة 350×300
            choice_dialog.setLayoutDirection(Qt.RightToLeft)
            choice_dialog.setStyleSheet("""
                QDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #3498db;
                    border-radius: 10px;
                }
                QLabel {
                    color: #2980b9;
                    font-weight: bold;
                    font-size: 14pt;
                }
                QComboBox {
                    border: 1px solid #3498db;
                    border-radius: 5px;
                    padding: 5px;
                    background-color: white;
                    color: black;
                    font-family: Calibri;
                    font-size: 12pt;
                    font-weight: bold;
                    min-height: 30px;
                }
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border-radius: 5px;
                    padding: 8px 15px;
                    font-weight: bold;
                    min-height: 35px;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                    border: 2px solid #3498db;
                }
            """)

            # إضافة أيقونة البرنامج
            try:
                app_icon = QIcon("01.ico")
                choice_dialog.setWindowIcon(app_icon)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج: {e}")

            # إنشاء تخطيط النافذة
            layout = QVBoxLayout(choice_dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إضافة عنوان
            title_label = QLabel("الرجاء اختر من القائمة مادة الامتحان")
            title_label.setFont(QFont("Calibri", 14, QFont.Bold))
            title_label.setStyleSheet("color: #000080;")  # أزرق غامق
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # إضافة مربع الاختيار للمواد
            subject_label = QLabel("اختر المادة:")
            subject_label.setFont(QFont("Calibri", 12, QFont.Bold))
            subject_label.setStyleSheet("color: black;")
            layout.addWidget(subject_label)

            # إضافة مربع الاختيار
            combo_box = QComboBox()
            combo_box.addItem("بدون مواد (ملء يدوي)")

            # الحصول على قائمة المواد من المصدر الوحيد: جدول جدولة_الامتحان
            subjects = []
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # الحصول على السنة الدراسية الحالية والأسدس
                cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
                result = cursor.fetchone()
                academic_year = result[0] if result and result[0] else ""
                semester = result[1] if result and result[1] else ""

                # استخراج المواد من جدول جدولة_الامتحان - المصدر الوحيد
                cursor.execute("""
                    SELECT اليوم, التاريخ,
                           الحصة1, التوقيت1,
                           الحصة2, التوقيت2,
                           الحصة3, التوقيت3,
                           الحصة4, التوقيت4
                    FROM جدولة_الامتحان
                    WHERE السنة_الدراسية = ? AND الأسدس = ?
                    ORDER BY id
                """, (academic_year, semester))

                schedule_data = cursor.fetchall()

                # استخراج المواد من بيانات الجدولة
                for row_data in schedule_data:
                    date = row_data[1]  # التاريخ
                    # استخراج الحصص الأربع
                    for i in range(4):
                        subject = row_data[2 + i*2]  # المادة
                        time = row_data[3 + i*2]     # التوقيت
                        if subject and time:
                            subjects.append((subject, date, time))

                conn.close()
            except Exception as e:
                print(f"خطأ في الحصول على قائمة المواد من جدول جدولة_الامتحان: {e}")

            # إضافة المواد إلى مربع الاختيار
            if subjects:
                for subject, date, time in subjects:
                    combo_box.addItem(f"{subject} - {date} - {time}")

            layout.addWidget(combo_box)

            # إضافة أزرار موافق/إلغاء
            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.button(QDialogButtonBox.Ok).setText("موافق")
            button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
            button_box.setCenterButtons(True)

            # ربط الأزرار بالإجراءات
            button_box.accepted.connect(choice_dialog.accept)
            button_box.rejected.connect(choice_dialog.reject)

            layout.addWidget(button_box)

            # عرض النافذة وانتظار الرد
            if choice_dialog.exec_() != QDialog.Accepted:
                return

            # الحصول على الاختيار
            selected_option = combo_box.currentText()

            # تحديد بيانات المادة
            subject_data = None
            if selected_option != "بدون مواد (ملء يدوي)":
                # استخراج بيانات المادة من النص
                parts = selected_option.split(" - ")
                if len(parts) >= 3:
                    subject_data = {
                        "المادة": parts[0],
                        "التاريخ": parts[1],
                        "التوقيت": parts[2]
                    }

            # محاولة استدعاء دالة إنشاء محضر التوقيعات من ملف print11.py
            try:
                # إنشاء شريط تقدم العملية محسن
                progress = QProgressDialog("جاري إنشاء لائحة الحضور...", "إلغاء", 0, 100, self)
                progress.setWindowTitle("إنشاء لائحة الحضور")
                progress.setWindowModality(Qt.WindowModal)
                progress.setMinimumDuration(0)
                progress.setAutoClose(True)
                progress.setAutoReset(True)
                progress.setMinimumWidth(400)

                # تحسين مظهر شريط التقدم
                progress.setStyleSheet("""
                    QProgressDialog {
                        background-color: #f0f8ff;
                        border: 2px solid #3498db;
                        border-radius: 10px;
                        padding: 10px;
                    }
                    QLabel {
                        color: #2980b9;
                        font-family: Calibri;
                        font-size: 12pt;
                        font-weight: bold;
                    }
                    QProgressBar {
                        border: 1px solid #3498db;
                        border-radius: 5px;
                        background-color: #ecf0f1;
                        text-align: center;
                        color: black;
                        font-family: Calibri;
                        font-weight: bold;
                        min-height: 20px;
                    }
                    QProgressBar::chunk {
                        background-color: #3498db;
                        width: 10px;
                        margin: 0.5px;
                    }
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border-radius: 5px;
                        padding: 5px 10px;
                        font-family: Calibri;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                    }
                """)

                progress.setValue(0)
                progress.show()

                # استيراد وحدة time للتأخير
                import time

                # تحديث شريط التقدم - بدء العملية
                progress.setLabelText("جاري تهيئة العملية...")
                progress.setValue(10)
                time.sleep(0.3)  # إضافة تأخير قصير لعرض التقدم

                # استيراد ملف print11.py
                import print11

                # تحديث شريط التقدم - جلب البيانات
                progress.setLabelText("جاري جلب بيانات المترشحين...")
                progress.setValue(30)
                time.sleep(0.3)

                # تحديث شريط التقدم - إنشاء التقرير
                progress.setLabelText("جاري إنشاء ملف PDF...")
                progress.setValue(50)

                # استدعاء دالة إنشاء التقرير مع تمرير بيانات المادة
                success, output_path, message = print11.print_exams_report(
                    parent=self,
                    report_title=title2,
                    subject_data=subject_data
                )

                # تحديث شريط التقدم - اكتمال العملية
                progress.setLabelText("جاري فتح الملف...")
                progress.setValue(80)
                QApplication.processEvents()  # معالجة الأحداث المعلقة
                time.sleep(0.3)

                # تحديث شريط التقدم - اكتمال العملية
                progress.setLabelText("اكتملت العملية بنجاح!")
                progress.setValue(100)
                QApplication.processEvents()  # معالجة الأحداث المعلقة
                time.sleep(0.5)

                if success:
                    self.show_message("نجاح", f"تم إنشاء لائحة الحضور بنجاح وفتحها.\nمسار الملف: {output_path}", "success")
                else:
                    self.show_message("تنبيه", f"لم يتم إنشاء لائحة الحضور: {message}", "warning")
            except ImportError:
                self.show_message("خطأ", "لم يتم العثور على ملف print11.py اللازم لإنشاء لائحة الحضور.", "error")
            except Exception as e:
                self.show_message("خطأ", f"حدث خطأ أثناء إنشاء لائحة الحضور: {str(e)}", "error")
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إنشاء لائحة الحضور: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def candidate_calls(self):
        """إنشاء استدعاءات المترشحين"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("استدعاءات المترشحين - إدارة التقارير واللوائح والاستدعاءات")

            # التحقق من إدخال البيانات المطلوبة
            title1 = self.text_fields["العنوان1"].text().strip()

            if not title1:
                self.show_message("تنبيه", "الرجاء إدخال عنوان الاستدعاء على الأقل.", "warning")
                return

            # إنشاء نافذة حوار للاختيار بين طباعة جميع الاستدعاءات أو حسب المؤسسة الأصلية
            choice_dialog = QDialog(self)
            choice_dialog.setWindowTitle("اختيار طريقة الطباعة")
            choice_dialog.setFixedSize(500, 400)  # تكبير النافذة إلى 500×400
            choice_dialog.setLayoutDirection(Qt.RightToLeft)
            choice_dialog.setStyleSheet("""
                QDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #3498db;
                    border-radius: 10px;
                }
                QLabel {
                    color: #2980b9;
                    font-weight: bold;
                    font-size: 14pt;
                }
                QComboBox {
                    border: 1px solid #3498db;
                    border-radius: 5px;
                    padding: 5px;
                    background-color: white;
                    color: black;
                    font-family: Calibri;
                    font-size: 12pt;
                    font-weight: bold;
                    min-height: 30px;
                }
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border-radius: 5px;
                    padding: 8px 15px;
                    font-weight: bold;
                    min-height: 35px;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                    border: 2px solid #3498db;
                }
            """)

            # إضافة أيقونة البرنامج
            try:
                app_icon = QIcon("01.ico")
                choice_dialog.setWindowIcon(app_icon)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج: {e}")

            # إنشاء تخطيط النافذة
            layout = QVBoxLayout(choice_dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إضافة عنوان
            title_label = QLabel("اختر طريقة طباعة الاستدعاءات")
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # إضافة مربع الاختيار للمؤسسات
            institution_label = QLabel("اختر المؤسسة:")
            institution_label.setFont(QFont("Calibri", 12, QFont.Bold))
            institution_label.setStyleSheet("color: black;")
            layout.addWidget(institution_label)

            # إضافة مربع الاختيار
            combo_box = QComboBox()
            combo_box.addItem("طباعة جميع الاستدعاءات")

            # الحصول على قائمة المؤسسات الأصلية من قاعدة البيانات
            institutions = []
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # محاولة الحصول على المؤسسات الأصلية (مع مراعاة الاسمين المحتملين للعمود)
                try:
                    cursor.execute("SELECT DISTINCT \"المؤسسة_الأصلية\" FROM امتحانات WHERE \"المؤسسة_الأصلية\" IS NOT NULL AND \"المؤسسة_الأصلية\" != ''")
                    institutions = [row[0] for row in cursor.fetchall()]
                except:
                    try:
                        cursor.execute("SELECT DISTINCT \"المؤسسة-الأصلية\" FROM امتحانات WHERE \"المؤسسة-الأصلية\" IS NOT NULL AND \"المؤسسة-الأصلية\" != ''")
                        institutions = [row[0] for row in cursor.fetchall()]
                    except:
                        pass

                conn.close()
            except Exception as e:
                print(f"خطأ في الحصول على قائمة المؤسسات: {e}")

            # إضافة المؤسسات إلى مربع الاختيار مع عدد الاستدعاءات لكل مؤسسة
            if institutions:
                # الحصول على عدد المترشحين لكل مؤسسة
                institution_counts = {}
                try:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()

                    # محاولة الحصول على عدد المترشحين لكل مؤسسة (مع مراعاة الاسمين المحتملين للعمود)
                    try:
                        cursor.execute("SELECT \"المؤسسة_الأصلية\", COUNT(*) FROM امتحانات WHERE \"المؤسسة_الأصلية\" IS NOT NULL AND \"المؤسسة_الأصلية\" != '' GROUP BY \"المؤسسة_الأصلية\"")
                        institution_counts = {row[0]: row[1] for row in cursor.fetchall()}
                    except:
                        try:
                            cursor.execute("SELECT \"المؤسسة-الأصلية\", COUNT(*) FROM امتحانات WHERE \"المؤسسة-الأصلية\" IS NOT NULL AND \"المؤسسة-الأصلية\" != '' GROUP BY \"المؤسسة-الأصلية\"")
                            institution_counts = {row[0]: row[1] for row in cursor.fetchall()}
                        except:
                            pass

                    conn.close()
                except Exception as e:
                    print(f"خطأ في الحصول على عدد المترشحين لكل مؤسسة: {e}")

                # إضافة المؤسسات مع عدد المترشحين
                for institution in institutions:
                    count = institution_counts.get(institution, 0)
                    combo_box.addItem(f"المؤسسة: {institution} ({count} استدعاء)")

            layout.addWidget(combo_box)

            # إضافة خيار طباعة استدعائين في صفحة واحدة
            layout.addSpacing(10)

            print_options_label = QLabel("خيارات الطباعة:")
            print_options_label.setFont(QFont("Calibri", 12, QFont.Bold))
            print_options_label.setStyleSheet("color: black;")
            layout.addWidget(print_options_label)

            # إضافة مربع اختيار لطباعة استدعائين في صفحة واحدة
            from PyQt5.QtWidgets import QCheckBox

            two_per_page_checkbox = QCheckBox("طباعة استدعائين في صفحة واحدة")
            two_per_page_checkbox.setFont(QFont("Calibri", 12))
            two_per_page_checkbox.setStyleSheet("""
                QCheckBox {
                    color: black;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:unchecked {
                    border: 2px solid #3498db;
                    background-color: white;
                    border-radius: 3px;
                }
                QCheckBox::indicator:checked {
                    border: 2px solid #3498db;
                    background-color: #3498db;
                    border-radius: 3px;
                }
            """)
            layout.addWidget(two_per_page_checkbox)

            # إضافة شرح للخيار
            option_description = QLabel("هذا الخيار يتيح طباعة استدعائين في صفحة واحدة لتوفير الورق")
            option_description.setFont(QFont("Calibri", 10))
            option_description.setStyleSheet("color: #666666; font-style: italic;")
            option_description.setWordWrap(True)
            layout.addWidget(option_description)

            layout.addSpacing(10)

            # إضافة أزرار موافق/إلغاء
            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.button(QDialogButtonBox.Ok).setText("موافق")
            button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
            button_box.setCenterButtons(True)

            # ربط الأزرار بالإجراءات
            button_box.accepted.connect(choice_dialog.accept)
            button_box.rejected.connect(choice_dialog.reject)

            layout.addWidget(button_box)

            # عرض النافذة وانتظار الرد
            if choice_dialog.exec_() != QDialog.Accepted:
                return

            # الحصول على الاختيار
            selected_option = combo_box.currentText()

            # الحصول على قيمة مربع اختيار طباعة استدعائين في صفحة واحدة
            two_per_page = two_per_page_checkbox.isChecked()

            # تحديد معايير التصفية
            filter_criteria = None
            if selected_option.startswith("المؤسسة:"):
                # استخراج اسم المؤسسة من النص (إزالة عدد الاستدعاءات)
                institution_text = selected_option.replace("المؤسسة: ", "")
                institution_name = institution_text.split(" (")[0]
                filter_criteria = {"المؤسسة_الأصلية": institution_name}

            # إنشاء شريط تحميل منسق وجميل
            progress = QProgressDialog("جاري التحضير لإنشاء استدعاءات المترشحين...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("إنشاء استدعاءات المترشحين")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setAutoClose(False)
            progress.setAutoReset(False)
            progress.setMinimumWidth(650)
            progress.setMinimumHeight(200)

            # إضافة أيقونة البرنامج لشريط التحميل
            try:
                app_icon = QIcon("01.ico")
                progress.setWindowIcon(app_icon)
            except:
                pass

            # تحسين مظهر شريط التحميل مع تصميم عصري وجذاب
            progress.setStyleSheet("""
                QProgressDialog {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #f8f9fa, stop: 0.5 #e9ecef, stop: 1 #dee2e6);
                    border: 3px solid #007bff;
                    border-radius: 20px;
                    padding: 25px;
                    font-family: 'Segoe UI', 'Calibri';
                }
                QLabel {
                    color: #495057;
                    font-family: 'Segoe UI', 'Calibri';
                    font-size: 14pt;
                    font-weight: bold;
                    background: transparent;
                    padding: 10px;
                    border: none;
                    text-align: center;
                }
                QProgressBar {
                    border: 3px solid #007bff;
                    border-radius: 15px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #ffffff, stop: 1 #f8f9fa);
                    text-align: center;
                    color: #212529;
                    font-family: 'Segoe UI', 'Calibri';
                    font-weight: bold;
                    font-size: 13pt;
                    min-height: 35px;
                    max-height: 35px;
                }
                QProgressBar::chunk {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                               stop: 0 #28a745, stop: 0.3 #20c997, 
                                               stop: 0.6 #17a2b8, stop: 1 #007bff);
                    border-radius: 12px;
                    margin: 2px;
                    animation: progress-animation 2s infinite;
                }
                QPushButton {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #dc3545, stop: 1 #c82333);
                    color: white;
                    border-radius: 12px;
                    padding: 12px 20px;
                    font-family: 'Segoe UI', 'Calibri';
                    font-weight: bold;
                    font-size: 12pt;
                    min-width: 140px;
                    border: 3px solid #c82333;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #c82333, stop: 1 #bd2130);
                    border: 3px solid #dc3545;
                    transform: scale(1.05);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #bd2130, stop: 1 #a71e2a);
                    transform: scale(0.98);
                }
            """)

            # إظهار شريط التحميل
            progress.setValue(0)
            progress.show()
            QApplication.processEvents()
            time.sleep(0.2)

            # محاولة استدعاء دالة إنشاء استدعاءات المترشحين
            try:
                # المرحلة 1: التحضير الأولي
                progress.setLabelText("🔧 جاري تحضير البيانات والمعايير...")
                progress.setValue(5)
                QApplication.processEvents()
                time.sleep(0.3)

                print("🚀 بدء عملية إنشاء استدعاءات المترشحين...")

                # المرحلة 2: إعداد المجلدات
                progress.setLabelText("📁 جاري إعداد مجلدات الحفظ...")
                progress.setValue(10)
                QApplication.processEvents()
                time.sleep(0.2)

                # الحصول على مجلد التنزيلات
                downloads_folder = get_downloads_folder()

                # إنشاء مجلد للتقارير داخل مجلد التنزيلات
                reports_folder = os.path.join(downloads_folder, "تقارير برنامج المعين في الحراسة العامة")
                os.makedirs(reports_folder, exist_ok=True)

                # المرحلة 3: جلب بيانات المترشحين
                progress.setLabelText("📊 جاري جلب بيانات المترشحين من قاعدة البيانات...")
                progress.setValue(20)
                QApplication.processEvents()
                time.sleep(0.3)

                # حساب عدد المترشحين المتوقع
                total_candidates = 0
                try:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    if filter_criteria:
                        if "المؤسسة_الأصلية" in filter_criteria:
                            cursor.execute("SELECT COUNT(*) FROM امتحانات WHERE \"المؤسسة_الأصلية\" = ?", 
                                         (filter_criteria["المؤسسة_الأصلية"],))
                    else:
                        cursor.execute("SELECT COUNT(*) FROM امتحانات")
                    
                    total_candidates = cursor.fetchone()[0]
                    conn.close()
                except Exception as e:
                    print(f"خطأ في حساب عدد المترشحين: {e}")

                # المرحلة 4: اختيار نوع التقرير
                progress.setLabelText(f"🎯 جاري اختيار نوع التقرير ({total_candidates} مترشح)...")
                progress.setValue(30)
                QApplication.processEvents()
                time.sleep(0.2)

                # استدعاء الملف المناسب بناءً على خيار طباعة استدعائين في صفحة واحدة
                if two_per_page:
                    # استخدام print14.py لطباعة استدعائين في صفحة واحدة أفقية
                    try:
                        progress.setLabelText("📄 جاري تحميل وحدة الطباعة الأفقية...")
                        progress.setValue(40)
                        QApplication.processEvents()
                        time.sleep(0.2)

                        import print14
                        
                        progress.setLabelText("🖨️ جاري إنشاء ملف PDF (استدعائين في صفحة واحدة أفقية)...")
                        progress.setValue(50)
                        QApplication.processEvents()
                        time.sleep(0.3)
                        
                        # استدعاء دالة إنشاء التقرير من ملف print14.py مع تمرير شريط التحميل
                        success, output_path, message = print14.print_horizontal_invitations(
                            parent=self,
                            level=None,  # يمكن تمرير المستوى إذا تم تحديده
                            report_title=title1,
                            filter_criteria=filter_criteria,
                            output_dir=reports_folder
                        )
                        
                        # تحديث التقدم بعد اكتمال print14
                        progress.setLabelText("✅ تم إنشاء الاستدعاءات الأفقية بنجاح!")
                        progress.setValue(90)
                        QApplication.processEvents()
                        time.sleep(0.5)
                        
                    except ImportError:
                        progress.close()
                        self.show_message("خطأ", "لم يتم العثور على ملف print14.py اللازم لإنشاء استدعاءات المترشحين في صفحة واحدة أفقياً.", "error")
                        return
                    except Exception as e:
                        progress.close()
                        self.show_message("خطأ", f"حدث خطأ أثناء إنشاء استدعاءات المترشحين (أفقي): {str(e)}", "error")
                        return
                else:
                    # استخدام print12.py للطباعة العادية (استدعاء واحد في كل صفحة)
                    try:
                        progress.setLabelText("📄 جاري تحميل وحدة الطباعة العادية...")
                        progress.setValue(40)
                        QApplication.processEvents()
                        time.sleep(0.2)

                        import print12
                        
                        progress.setLabelText("🖨️ جاري إنشاء ملف PDF (استدعاء واحد في كل صفحة)...")
                        progress.setValue(50)
                        QApplication.processEvents()
                        time.sleep(0.3)
                        
                        # استدعاء دالة إنشاء التقرير من ملف print12.py
                        success, output_path, message = print12.print_exams_report(
                            parent=self,
                            report_title=title1,
                            sub_title=title1,
                            filter_criteria=filter_criteria,
                            output_dir=reports_folder,
                            two_per_page=False  # تعطيل خيار استدعائين في صفحة واحدة للملف print12.py
                        )
                        
                        # تحديث التقدم بعد اكتمال print12
                        progress.setLabelText("✅ تم إنشاء الاستدعاءات العادية بنجاح!")
                        progress.setValue(90)
                        QApplication.processEvents()
                        time.sleep(0.5)
                        
                    except ImportError:
                        progress.close()
                        self.show_message("خطأ", "لم يتم العثور على ملف print12.py اللازم لإنشاء استدعاءات المترشحين.", "error")
                        return
                    except Exception as e:
                        progress.close()
                        self.show_message("خطأ", f"حدث خطأ أثناء إنشاء استدعاءات المترشحين: {str(e)}", "error")
                        return

                # المرحلة الأخيرة: فتح الملف
                progress.setLabelText("📂 جاري فتح الملف المُنشأ...")
                progress.setValue(95)
                QApplication.processEvents()
                time.sleep(0.3)

                print("📂 فتح الملف...")

                # إتمام شريط التحميل
                progress.setLabelText("🎉 اكتملت العملية بنجاح! جاري عرض النتائج...")
                progress.setValue(100)
                QApplication.processEvents()
                time.sleep(1.0)

                # إغلاق شريط التحميل
                progress.close()

                if success:
                    # تحديد عدد الاستدعاءات التي تم إنشاؤها
                    num_invitations = 0
                    if selected_option.startswith("المؤسسة:"):
                        # استخراج عدد الاستدعاءات من النص
                        try:
                            count_text = selected_option.split("(")[1].split(" استدعاء")[0]
                            num_invitations = int(count_text)
                        except:
                            pass
                    else:
                        # الحصول على إجمالي عدد المترشحين
                        try:
                            conn = sqlite3.connect(self.db_path)
                            cursor = conn.cursor()
                            cursor.execute("SELECT COUNT(*) FROM امتحانات")
                            num_invitations = cursor.fetchone()[0]
                            conn.close()
                        except:
                            pass

                    # إنشاء رسالة النجاح المحسنة
                    success_message = f"✅ تم إنشاء استدعاءات المترشحين بنجاح وفتحها!\n\n"
                    success_message += f"📊 تفاصيل العملية:\n"
                    if num_invitations > 0:
                        success_message += f"• عدد الاستدعاءات المُنشأة: {num_invitations}\n"
                    
                    if two_per_page:
                        pages_count = (num_invitations + 1) // 2
                        success_message += f"• نوع الطباعة: استدعائين في صفحة واحدة (أفقي)\n"
                        success_message += f"• عدد الصفحات المتوقع: {pages_count}\n"
                    else:
                        success_message += f"• نوع الطباعة: استدعاء واحد في كل صفحة (عمودي)\n"
                        success_message += f"• عدد الصفحات المتوقع: {num_invitations}\n"
                    
                    if filter_criteria and "المؤسسة_الأصلية" in filter_criteria:
                        success_message += f"• المؤسسة المحددة: {filter_criteria['المؤسسة_الأصلية']}\n"
                    else:
                        success_message += f"• النطاق: جميع المترشحين\n"
                    
                    success_message += f"\n📁 مسار الملف:\n{output_path}"

                    self.show_message(
                        "نجح إنشاء الاستدعاءات!",
                        success_message,
                        "success"
                    )
                else:
                    self.show_message("تنبيه", f"لم يتم إنشاء استدعاءات المترشحين: {message}", "warning")

            except Exception as e:
                # إغلاق شريط التحميل في حالة الخطأ
                if 'progress' in locals():
                    progress.close()
                self.show_message("خطأ", f"حدث خطأ أثناء إنشاء استدعاءات المترشحين: {str(e)}", "error")
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إنشاء استدعاءات المترشحين: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def statistics(self):
        """إنشاء تقرير الإحصاءات"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("إحصاءات الامتحانات - إدارة التقارير واللوائح والاستدعاءات")

            # التحقق من إدخال البيانات المطلوبة
            title3 = self.text_fields["العنوان3"].text().strip()

            if not title3:
                self.show_message("تنبيه", "الرجاء إدخال عنوان الإحصائيات على الأقل.", "warning")
                return

            # إنشاء شريط تقدم العملية محسن
            progress = QProgressDialog("جاري إنشاء تقرير الإحصائيات...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("إنشاء تقرير الإحصائيات")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setAutoClose(True)
            progress.setAutoReset(True)
            progress.setMinimumWidth(400)

            # تحسين مظهر شريط التقدم
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #3498db;
                    border-radius: 10px;
                    padding: 10px;
                }
                QLabel {
                    color: #2980b9;
                    font-family: Calibri;
                    font-size: 12pt;
                    font-weight: bold;
                }
                QProgressBar {
                    border: 1px solid #3498db;
                    border-radius: 5px;
                    background-color: #ecf0f1;
                    text-align: center;
                    color: black;
                    font-family: Calibri;
                    font-weight: bold;
                    min-height: 20px;
                }
                QProgressBar::chunk {
                    background-color: #3498db;
                    width: 10px;
                    margin: 0.5px;
                }
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border-radius: 5px;
                    padding: 5px 10px;
                    font-family: Calibri;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)

            progress.setValue(0)
            progress.show()

            # استيراد وحدة time للتأخير
            import time

            # تحديث شريط التقدم - بدء العملية
            progress.setLabelText("جاري تهيئة العملية...")
            progress.setValue(10)
            time.sleep(0.3)

            # محاولة استدعاء دالة إنشاء تقرير الإحصاءات من ملف print15.py
            try:
                # تحديث شريط التقدم - جلب البيانات
                progress.setLabelText("جاري جلب بيانات المترشحين...")
                progress.setValue(30)
                time.sleep(0.3)

                # استيراد ملف print15
                import print15

                # تحديث شريط التقدم - إنشاء التقرير
                progress.setLabelText("جاري إنشاء ملف PDF...")
                progress.setValue(50)

                # الحصول على مجلد التنزيلات
                downloads_folder = get_downloads_folder()
                
                # إنشاء مجلد للتقارير داخل مجلد التنزيلات
                reports_folder = os.path.join(downloads_folder, "تقارير برنامج المعين في الحراسة العامة")
                os.makedirs(reports_folder, exist_ok=True)

                # تحديد اسم الملف مع طابع زمني
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_filename = f"تقرير_الاحصاءات_{timestamp}.pdf"
                output_path = os.path.join(reports_folder, output_filename)

                # استدعاء دالة إنشاء تقرير الإحصاءات
                success, final_output_path, message = print15.print_exams_report(
                    parent=self,
                    level=None,  # جميع المستويات
                    report_title=title3,
                    subject_data=None
                )

                # تحديث شريط التقدم - اكتمال العملية
                progress.setLabelText("جاري فتح الملف...")
                progress.setValue(80)
                QApplication.processEvents()  # معالجة الأحداث المعلقة
                time.sleep(0.3)

                # تحديث شريط التقدم - اكتمال العملية
                progress.setLabelText("اكتملت العملية بنجاح!")
                progress.setValue(100)
                QApplication.processEvents()  # معالجة الأحداث المعلقة
                time.sleep(0.5)

                if success:
                    # الحصول على عدد المترشحين الإجمالي
                    total_candidates = 0
                    total_rooms = 0
                    total_levels = 0
                    
                    try:
                        conn = sqlite3.connect(self.db_path)
                        cursor = conn.cursor()
                        
                        # عدد المترشحين الإجمالي
                        cursor.execute("SELECT COUNT(*) FROM امتحانات")
                        total_candidates = cursor.fetchone()[0]
                        
                        # عدد القاعات
                        cursor.execute("SELECT COUNT(DISTINCT القاعة) FROM امتحانات WHERE القاعة IS NOT NULL AND القاعة != ''")
                        total_rooms = cursor.fetchone()[0]
                        
                        # عدد المستويات
                        cursor.execute("SELECT COUNT(DISTINCT المستوى) FROM امتحانات WHERE المستوى IS NOT NULL AND المستوى != ''")
                        total_levels = cursor.fetchone()[0]
                        
                        conn.close()
                    except Exception as e:
                        print(f"خطأ في الحصول على الإحصاءات: {e}")

                    # إنشاء رسالة النجاح التفصيلية
                    success_message = f"تم إنشاء تقرير الإحصاءات بنجاح وفتحه.\n\n"
                    success_message += f"ملخص الإحصاءات:\n"
                    success_message += f"• إجمالي المترشحين: {total_candidates}\n"
                    success_message += f"• عدد القاعات: {total_rooms}\n"
                    success_message += f"• عدد المستويات: {total_levels}\n\n"
                    success_message += f"تم حفظ الملف في مجلد التنزيلات:\n{final_output_path or output_path}"

                    self.show_message("نجاح", success_message, "success")
                else:
                    self.show_message("تنبيه", f"لم يتم إنشاء تقرير الإحصاءات: {message}", "warning")

            except ImportError:
                self.show_message("خطأ", "لم يتم العثور على ملف print15.py اللازم لإنشاء تقرير الإحصاءات.", "error")
            except Exception as e:
                self.show_message("خطأ", f"حدث خطأ أثناء إنشاء تقرير الإحصاءات: {str(e)}", "error")

        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إنشاء تقرير الإحصاءات: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def attendance_all_subjects(self):
        """إنشاء لوائح المترشحين"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("لوائح المترشحين - إدارة التقارير واللوائح والاستدعاءات")

            # التحقق من إدخال البيانات المطلوبة
            title2 = self.text_fields["العنوان2"].text().strip()

            if not title2:
                self.show_message("تنبيه", "الرجاء إدخال عنوان لائحة الحضور على الأقل.", "warning")
                return

            # إنشاء شريط تقدم العملية محسن
            progress = QProgressDialog("جاري إنشاء لوائح المترشحين...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("إنشاء لوائح المترشحين")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setAutoClose(True)
            progress.setAutoReset(True)
            progress.setMinimumWidth(400)

            # تحسين مظهر شريط التقدم
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #3498db;
                    border-radius: 10px;
                    padding: 10px;
                }
                QLabel {
                    color: #2980b9;
                    font-family: Calibri;
                    font-size: 12pt;
                    font-weight: bold;
                }
                QProgressBar {
                    border: 1px solid #3498db;
                    border-radius: 5px;
                    background-color: #ecf0f1;
                    text-align: center;
                    color: black;
                    font-family: Calibri;
                    font-weight: bold;
                    min-height: 20px;
                }
                QProgressBar::chunk {
                    background-color: #3498db;
                    width: 10px;
                    margin: 0.5px;
                }
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border-radius: 5px;
                    padding: 5px 10px;
                    font-family: Calibri;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)

            progress.setValue(0)
            progress.show()

            # استيراد وحدة time للتأخير
            import time

            # تحديث شريط التقدم - بدء العملية
            progress.setLabelText("جاري تهيئة العملية...")
            progress.setValue(10)
            time.sleep(0.3)  # إضافة تأخير قصير لعرض التقدم

            # استعلام عن جميع المستويات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT DISTINCT المستوى FROM امتحانات
                ORDER BY المستوى
            """)

            levels = cursor.fetchall()
            conn.close()

            if not levels:
                self.show_message("تنبيه", "لم يتم العثور على أي مستويات في قاعدة البيانات.", "warning")
                return

            # تحديث شريط التقدم - جلب البيانات
            progress.setLabelText("جاري جلب بيانات المترشحين...")
            progress.setValue(30)
            QApplication.processEvents()
            time.sleep(0.3)

            # محاولة إنشاء لوائح المترشحين لجميع المستويات
            success_count = 0
            try:
                # تحديث شريط التقدم - إنشاء التقرير
                progress.setLabelText("جاري إنشاء ملفات PDF...")
                progress.setValue(50)
                QApplication.processEvents()
                time.sleep(0.3)

                # استيراد ملف print13.py
                import print13

                for i, level in enumerate(levels):
                    level_name = level[0]
                    # تحديث شريط التقدم لكل مستوى
                    progress.setLabelText(f"جاري إنشاء لائحة المترشحين للمستوى: {level_name}")
                    progress.setValue(50 + (i * 30 // len(levels)))
                    QApplication.processEvents()  # معالجة الأحداث المعلقة

                    success, _, _ = print13.print_exams_report(self, level=level_name, report_title=title2)
                    if success:
                        success_count += 1

                # تحديث شريط التقدم - اكتمال العملية
                progress.setLabelText("اكتملت العملية بنجاح!")
                progress.setValue(100)
                QApplication.processEvents()  # معالجة الأحداث المعلقة
                time.sleep(0.5)

            except ImportError:
                self.show_message("خطأ", "لم يتم العثور على ملف print13.py اللازم لإنشاء لوائح المترشحين.", "error")
                return
            except Exception as e:
                self.show_message("خطأ", f"حدث خطأ أثناء إنشاء لوائح المترشحين: {str(e)}", "error")
                return

            if success_count > 0:
                self.show_message("نجاح", f"تم إنشاء {success_count} لائحة مترشحين بنجاح.", "success")
            else:
                self.show_message("تنبيه", "لم يتم إنشاء أي لائحة مترشحين.", "warning")
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إنشاء لوائح المترشحين: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def table_labels(self):
        """إنشاء ملصقات الطاولات"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("ملصقات الطاولات - إدارة التقارير واللوائح والاستدعاءات")

            # التحقق من إدخال البيانات المطلوبة
            title4 = self.text_fields["العنوان4"].text().strip()

            if not title4:
                self.show_message("تنبيه", "الرجاء إدخال عنوان جدولة الامتحان على الأقل.", "warning")
                return

            try:
                # استيراد نافذة ملصقات الطاولات من ملف sub27_window.py
                import importlib.util
            
                # تحديد مسار الملف
                module_path = os.path.join(os.path.dirname(__file__), 'sub27_window.py')
            
                # التحقق من وجود الملف
                if not os.path.exists(module_path):
                    self.show_message("خطأ", "ملف sub27_window.py غير موجود في المجلد الحالي.", "error")
                    return
                
                # استيراد الوحدة بشكل ديناميكي
                spec = importlib.util.spec_from_file_location("sub27_window", module_path)
                sub27_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(sub27_module)
            
                # نقوم بالتحقق من اسم الكلاس المستخدم في ملف sub27_window.py
                window = None
                if hasattr(sub27_module, "Sub27Window"):
                    # استخدام الفئة Sub27Window
                    window = sub27_module.Sub27Window(parent=self, db_path=self.db_path)
                elif hasattr(sub27_module, "TableLabelsPDF"):
                    # استخدام الفئة TableLabelsPDF (كما في sub22_window.py)
                    window = sub27_module.TableLabelsPDF(parent=self, db_path=self.db_path)
                else:
                    self.show_message("خطأ", "لم يتم العثور على الفئة المطلوبة في ملف sub27_window.py.", "error")
                    return
            
                # تمرير العنوان إذا كانت الفئة تدعم تعيين العنوان
                if hasattr(window, "set_title"):
                    window.set_title(title4)
            
                # عرض النافذة
                window.exec_()

            except ImportError as e:
                self.show_message("خطأ", f"تعذر استيراد ملف sub27_window.py: {str(e)}", "error")
            except Exception as e:
                self.show_message("خطأ", f"حدث خطأ أثناء فتح نافذة ملصقات الطاولات: {str(e)}", "error")
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إنشاء ملصقات الطاولات: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def general_instructions(self):
        """فتح نافذة التوجيهات العامة للمترشح"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("توجيهات عامة للمترشح (ة) - إدارة التقارير واللوائح والاستدعاءات")

            # إنشاء نافذة حوار جديدة
            instructions_dialog = QDialog(self)
            instructions_dialog.setWindowTitle("توجيهات عامة للمترشح (ة)")
            instructions_dialog.setFixedSize(1000, 510)
            instructions_dialog.setLayoutDirection(Qt.RightToLeft)

            # إضافة أيقونة البرنامج
            try:
                app_icon = QIcon("01.ico")
                instructions_dialog.setWindowIcon(app_icon)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج: {e}")

            # إنشاء تخطيط النافذة
            layout = QVBoxLayout(instructions_dialog)
            layout.setContentsMargins(5, 5, 5, 5)
            layout.setSpacing(5)

            # إضافة عنوان
            title_label = QLabel("توجيهات عامة للمترشح (ة)")
            title_label.setFont(QFont("Calibri", 18, QFont.Bold))
            title_label.setStyleSheet("color: #2980b9;")
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # إضافة مربع نص كبير مع محاذاة إلى اليمين
            instructions_text = QTextEdit()
            instructions_text.setFont(QFont("Calibri", 14, QFont.Bold))
            instructions_text.setStyleSheet("""
                QTextEdit {
                    border: 1px solid #3498db;
                    border-radius: 10px;
                    padding: 10px;
                    background-color: white;
                }
            """)
            instructions_text.setFixedSize(900, 400)

            # تعيين محاذاة النص إلى اليمين
            instructions_text.setLayoutDirection(Qt.RightToLeft)

            # تعيين محاذاة النص إلى اليمين باستخدام HTML
            instructions_text.setAlignment(Qt.AlignRight)

            # تحميل البيانات من قاعدة البيانات
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # التحقق من وجود جدول جدولة_الامتحان
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدولة_الامتحان'")
                table_exists = cursor.fetchone()

                if not table_exists:
                    # إنشاء جدول جدولة_الامتحان إذا لم يكن موجودًا
                    cursor.execute("""
                        CREATE TABLE جدولة_الامتحان (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            اليوم TEXT,
                            التاريخ TEXT,
                            الحصة1 TEXT,
                            التوقيت1 TEXT,
                            الحصة2 TEXT,
                            التوقيت2 TEXT,
                            الحصة3 TEXT,
                            التوقيت3 TEXT,
                            الحصة4 TEXT,
                            التوقيت4 TEXT,
                            السنة_الدراسية TEXT,
                            الأسدس TEXT,
                            تاريخ_التحديث TEXT,
                            ملاحظات TEXT
                        )
                    """)
                    conn.commit()
                    self.show_message("معلومات", "تم إنشاء جدول جدولة_الامتحان بنجاح.", "info")
                else:
                    # التحقق من وجود عمود ملاحظات في جدول جدولة_الامتحان
                    cursor.execute("PRAGMA table_info(جدولة_الامتحان)")
                    columns = cursor.fetchall()
                    has_notes_column = any(col[1] == "ملاحظات" for col in columns)

                    if not has_notes_column:
                        # إضافة عمود ملاحظات إذا لم يكن موجودًا
                        cursor.execute("ALTER TABLE جدولة_الامتحان ADD COLUMN ملاحظات TEXT")
                        conn.commit()

                # التحقق من وجود سجل بمعرف 1
                cursor.execute("SELECT id FROM جدولة_الامتحان WHERE id = 1")
                record = cursor.fetchone()

                if not record:
                    # إنشاء سجل جديد إذا لم يكن موجودًا
                    cursor.execute("""
                        INSERT INTO جدولة_الامتحان (id, ملاحظات)
                        VALUES (1, '')
                    """)
                    conn.commit()

                # استعلام عن البيانات
                cursor.execute("SELECT ملاحظات FROM جدولة_الامتحان WHERE id = 1")
                result = cursor.fetchone()

                if result:
                    # تعيين النص مع تطبيق محاذاة إلى اليمين
                    text_content = result[0] or ""
                    instructions_text.setText(text_content)

                    # تطبيق محاذاة إلى اليمين على النص
                    cursor = instructions_text.textCursor()
                    cursor.select(cursor.Document)
                    format = cursor.blockFormat()
                    format.setAlignment(Qt.AlignRight)
                    cursor.setBlockFormat(format)

                conn.close()
            except Exception as e:
                self.show_message("خطأ", f"حدث خطأ أثناء تحميل التوجيهات العامة: {str(e)}", "error")

            # إضافة مربع النص إلى التخطيط
            text_layout = QHBoxLayout()
            text_layout.addStretch()
            text_layout.addWidget(instructions_text)
            text_layout.addStretch()
            layout.addLayout(text_layout)

            # إضافة زر حفظ النص وإنشاء صورة
            save_button = QPushButton("حفظ النص وإنشاء صورة")
            save_button.setFont(QFont("Calibri", 12, QFont.Bold))
            save_button.setStyleSheet("""
                QPushButton {
                    background-color: #2ecc71;
                    color: white;
                    border-radius: 5px;
                    padding: 10px;
                    min-width: 150px;
                    min-height: 40px;
                }
                QPushButton:hover {
                    background-color: #27ae60;
                }
            """)
            save_button.setCursor(Qt.PointingHandCursor)

            # دالة لاختيار صورة من المتصفح
            def select_image_from_browser():
                try:
                    # فتح متصفح الملفات لاختيار صورة
                    file_dialog = QFileDialog()
                    file_dialog.setWindowTitle("اختيار صورة التوجيهات")
                    file_dialog.setFileMode(QFileDialog.ExistingFile)
                    file_dialog.setNameFilter("ملفات الصور (*.png *.jpg *.jpeg *.bmp *.gif)")
                    file_dialog.setViewMode(QFileDialog.Detail)

                    if file_dialog.exec_() == QFileDialog.Accepted:
                        selected_files = file_dialog.selectedFiles()
                        if selected_files:
                            source_image_path = selected_files[0]

                            # إنشاء مسار الوجهة
                            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                            app_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
                            images_folder = os.path.join(app_folder, "صور التوجيهات")

                            # التأكد من وجود المجلد
                            os.makedirs(images_folder, exist_ok=True)

                            # تحديد مسار الصورة الجديد
                            destination_image_path = os.path.join(images_folder, "توجيهات_المترشح.png")

                            # نسخ الصورة المختارة إلى المجلد المطلوب
                            import shutil
                            shutil.copy2(source_image_path, destination_image_path)

                            print(f"تم نسخ الصورة المختارة إلى: {destination_image_path}")
                            return True, destination_image_path

                    return False, None
                except Exception as e:
                    self.show_message("خطأ", f"حدث خطأ أثناء اختيار الصورة: {str(e)}", "error")
                    return False, None

            # دالة لتصوير مربع النص وحفظه كصورة مع محاذاة صحيحة
            def capture_text_as_image():
                try:
                    # إنشاء مسار للصورة
                    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                    app_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
                    images_folder = os.path.join(app_folder, "صور التوجيهات")

                    # التأكد من وجود المجلد
                    os.makedirs(images_folder, exist_ok=True)

                    # تحديد مسار الصورة
                    image_path = os.path.join(images_folder, "توجيهات_المترشح.png")

                    # تطبيق محاذاة إلى اليمين قبل التصوير
                    cursor = instructions_text.textCursor()
                    cursor.select(cursor.Document)
                    format = cursor.blockFormat()
                    format.setAlignment(Qt.AlignRight)
                    cursor.setBlockFormat(format)

                    # التأكد من تحديث العرض
                    instructions_text.update()
                    QApplication.processEvents()

                    # تصوير مربع النص
                    pixmap = instructions_text.grab()
                    pixmap.save(image_path, "PNG")

                    # عرض رسالة نجاح
                    print(f"تم حفظ صورة التوجيهات محاذية إلى اليمين في: {image_path}")

                    return True, image_path
                except Exception as e:
                    self.show_message("خطأ", f"حدث خطأ أثناء تصوير التوجيهات: {str(e)}", "error")
                    return False, None

            # دالة حفظ التوجيهات
            def save_instructions():
                try:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()

                    # التحقق من وجود سجل بمعرف 1
                    cursor.execute("SELECT id FROM جدولة_الامتحان WHERE id = 1")
                    record = cursor.fetchone()

                    if record:
                        # تحديث السجل الموجود
                        cursor.execute("UPDATE جدولة_الامتحان SET ملاحظات = ? WHERE id = 1", (instructions_text.toPlainText(),))
                    else:
                        # إنشاء سجل جديد
                        cursor.execute("INSERT INTO جدولة_الامتحان (id, ملاحظات) VALUES (1, ?)", (instructions_text.toPlainText(),))

                    conn.commit()
                    conn.close()

                    # تطبيق محاذاة إلى اليمين قبل الحفظ والتصوير
                    cursor = instructions_text.textCursor()
                    cursor.select(cursor.Document)
                    format = cursor.blockFormat()
                    format.setAlignment(Qt.AlignRight)
                    cursor.setBlockFormat(format)

                    # تصوير مربع النص وحفظه كصورة
                    capture_success = capture_text_as_image()

                    if capture_success:
                        self.show_message("نجاح", "تم حفظ التوجيهات العامة وتصويرها بمحاذاة صحيحة إلى اليمين بنجاح.", "success")
                    else:
                        self.show_message("نجاح", "تم حفظ التوجيهات العامة بنجاح، ولكن تعذر تصويرها.", "success")

                    instructions_dialog.accept()
                except Exception as e:
                    self.show_message("خطأ", f"حدث خطأ أثناء حفظ التوجيهات العامة: {str(e)}", "error")

            # إضافة زر اختيار صورة من المتصفح
            select_image_button = QPushButton("اختيار صورة من الجهاز")
            select_image_button.setFont(QFont("Calibri", 12, QFont.Bold))
            select_image_button.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border-radius: 5px;
                    padding: 10px;
                    min-width: 150px;
                    min-height: 40px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            select_image_button.setCursor(Qt.PointingHandCursor)

            # دالة لاختيار صورة من المتصفح
            def select_image_action():
                success, image_path = select_image_from_browser()
                if success:
                    # حفظ النص في قاعدة البيانات أيضاً
                    try:
                        conn = sqlite3.connect(self.db_path)
                        cursor = conn.cursor()

                        cursor.execute("SELECT id FROM جدولة_الامتحان WHERE id = 1")
                        record = cursor.fetchone()

                        if record:
                            cursor.execute("UPDATE جدولة_الامتحان SET ملاحظات = ? WHERE id = 1", (instructions_text.toPlainText(),))
                        else:
                            cursor.execute("INSERT INTO جدولة_الامتحان (id, ملاحظات) VALUES (1, ?)", (instructions_text.toPlainText(),))

                        conn.commit()
                        conn.close()

                        self.show_message("نجاح", f"تم اختيار الصورة وحفظ النص بنجاح.\n\nمسار الصورة:\n{image_path}", "success")
                        instructions_dialog.accept()
                    except Exception as e:
                        self.show_message("خطأ", f"تم اختيار الصورة ولكن حدث خطأ في حفظ النص: {str(e)}", "error")

            select_image_button.clicked.connect(select_image_action)
            save_button.clicked.connect(save_instructions)

            # إضافة تسمية توضيحية
            options_label = QLabel("اختر طريقة إنشاء صورة التوجيهات:")
            options_label.setFont(QFont("Calibri", 12, QFont.Bold))
            options_label.setStyleSheet("""
                QLabel {
                    color: #2c3e50;
                    padding: 10px;
                    background-color: #ecf0f1;
                    border-radius: 5px;
                    border: 1px solid #bdc3c7;
                }
            """)
            options_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(options_label)

            # إضافة الأزرار إلى التخطيط
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            button_layout.addWidget(select_image_button)
            button_layout.addSpacing(20)
            button_layout.addWidget(save_button)
            button_layout.addStretch()
            layout.addLayout(button_layout)

            # إضافة ملاحظة توضيحية
            note_label = QLabel("💡 يمكنك اختيار صورة جاهزة من جهازك أو إنشاء صورة من النص المكتوب أعلاه")
            note_label.setFont(QFont("Calibri", 10))
            note_label.setStyleSheet("""
                QLabel {
                    color: #7f8c8d;
                    padding: 5px;
                    font-style: italic;
                }
            """)
            note_label.setAlignment(Qt.AlignCenter)
            note_label.setWordWrap(True)
            layout.addWidget(note_label)

            # عرض النافذة
            instructions_dialog.exec_()
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء فتح نافذة التوجيهات العامة: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def collective_report(self):
        """إنشاء المحضر الجماعي للمترشحين"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("المحضر الجماعي للمترشحين - إدارة التقارير واللوائح والاستدعاءات")

            # التحقق من إدخال البيانات المطلوبة - استخدام العنوان5 بدلاً من العنوان2
            title5 = self.text_fields["العنوان5"].text().strip()

            if not title5:
                self.show_message("تنبيه", "الرجاء إدخال عنوان المحضر الجماعي على الأقل لاستخدامه كعنوان للمحضر الجماعي.", "warning")
                return

            # إنشاء شريط تقدم العملية محسن ومنسق
            progress = QProgressDialog("جاري إنشاء المحضر الجماعي للمترشحين...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("إنشاء المحضر الجماعي")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setAutoClose(True)
            progress.setAutoReset(True)
            progress.setMinimumWidth(500)
            progress.setMinimumHeight(150)

            # تحسين مظهر شريط التقدم بشكل جميل ومنسق
            progress.setStyleSheet("""
                QProgressDialog {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #f0f8ff, stop: 1 #e6f3ff);
                    border: 3px solid #1abc9c;
                    border-radius: 15px;
                    padding: 15px;
                    box-shadow: 0px 5px 15px rgba(0,0,0,0.3);
                }
                QLabel {
                    color: #2c3e50;
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    background: transparent;
                    padding: 5px;
                }
                QProgressBar {
                    border: 2px solid #1abc9c;
                    border-radius: 8px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #ecf0f1, stop: 1 #bdc3c7);
                    text-align: center;
                    color: #2c3e50;
                    font-family: 'Calibri';
                    font-weight: bold;
                    font-size: 11pt;
                    min-height: 25px;
                    max-height: 25px;
                }
                QProgressBar::chunk {
                    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                               stop: 0 #1abc9c, stop: 0.5 #16a085, stop: 1 #1abc9c);
                    border-radius: 6px;
                    margin: 1px;
                }
                QPushButton {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #dc3545, stop: 1 #c82333);
                    color: white;
                    border-radius: 12px;
                    padding: 12px 20px;
                    font-family: 'Segoe UI', 'Calibri';
                    font-weight: bold;
                    font-size: 12pt;
                    min-width: 140px;
                    border: 3px solid #c82333;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #c82333, stop: 1 #bd2130);
                    border: 3px solid #dc3545;
                    transform: scale(1.05);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #bd2130, stop: 1 #a71e2a);
                }
            """)

            progress.setValue(0)
            progress.show()

            # محاولة استدعاء دالة إنشاء المحضر الجماعي من ملف print313.py
            try:
                # تحديث شريط التقدم - بدء العملية
                progress.setLabelText("🔄 جاري تهيئة العملية...")
                progress.setValue(10)
                QApplication.processEvents()
                time.sleep(0.4)

                # تحديث شريط التقدم - تحميل الوحدة
                progress.setLabelText("📦 جاري تحميل وحدة إنشاء التقارير...")
                progress.setValue(20)
                QApplication.processEvents()
                time.sleep(0.3)

                # تحديث شريط التقدم - جلب البيانات
                progress.setLabelText("🗂️ جاري جلب بيانات المترشحين...")
                progress.setValue(30)
                QApplication.processEvents()
                time.sleep(0.4)

                # التحقق من وجود البيانات والحصول على الإحصائيات مسبقاً
                total_candidates = 0
                total_levels =  0
                gender_stats = {}
                age_stats = {}
                institution_stats = {}
                
                try:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    # عدد المترشحين الإجمالي
                    cursor.execute("SELECT COUNT(*) FROM امتحانات")
                    total_candidates = cursor.fetchone()[0]
                    
                    if total_candidates == 0:
                        conn.close()
                        progress.close()
                        self.show_message("تنبيه", "لا توجد بيانات مترشحين في قاعدة البيانات.", "warning")
                        return
                    
                    # عدد المستويات
                    cursor.execute("SELECT COUNT(DISTINCT المستوى) FROM امتحانات WHERE المستوى IS NOT NULL AND المستوى != ''")
                    total_levels = cursor.fetchone()[0]
                    
                    # إحصائيات الجنس
                    cursor.execute("SELECT الجنس, COUNT(*) FROM امتحانات WHERE الجنس IS NOT NULL GROUP BY الجنس")
                    for row in cursor.fetchall():
                        gender = row[0] if row[0] and row[0].strip() else 'غير محدد'
                        gender_stats[gender] = row[1]
                    
                    # إحصائيات العمر
                    cursor.execute("SELECT تاريخ_الازدياد FROM امتحانات WHERE تاريخ_الازدياد IS NOT NULL")
                    birth_dates = cursor.fetchall()
                    
                    for birth_date_row in birth_dates:
                        birth_date_str = birth_date_row[0]
                        age = self.calculate_age(birth_date_str)
                        age_display = str(age) if age != 999 else 'غير محدد'
                        age_stats[age_display] = age_stats.get(age_display, 0) + 1
                    
                    # ترتيب الأعمار من الأصغر للأكبر
                    sorted_age_stats = {}
                    numeric_ages = []
                    non_numeric_ages = {}
                    
                    for age_str, count in age_stats.items():
                        if age_str != 'غير محدد':
                            try:
                                numeric_ages.append((int(age_str), count))
                            except ValueError:
                                non_numeric_ages[age_str] = count
                        else:
                            non_numeric_ages[age_str] = count
                    
                    # ترتيب الأعمار الرقمية
                    numeric_ages.sort(key=lambda x: x[0])
                    for age, count in numeric_ages:
                        sorted_age_stats[str(age)] = count
                    
                    # إضافة غير المحددة في النهاية
                    for age_str, count in non_numeric_ages.items():
                        sorted_age_stats[age_str] = count
                    
                    age_stats = sorted_age_stats
                    
                    # إحصائيات المؤسسة الأصلية
                    cursor.execute("SELECT \"المؤسسة_الأصلية\", COUNT(*) FROM امتحانات WHERE \"المؤسسة_الأصلية\" IS NOT NULL GROUP BY \"المؤسسة_الأصلية\"")
                    for row in cursor.fetchall():
                        institution = row[0] if row[0] and row[0].strip() else 'غير محدد'
                        institution_stats[institution] = row[1]
                    
                    conn.close()
                except Exception as e:
                    print(f"خطأ في الحصول على الإحصائيات: {e}")
                    if 'conn' in locals():
                        conn.close()

                # تحديث شريط التقدم - معالجة البيانات
                progress.setLabelText("⚙️ جاري معالجة وتجميع البيانات...")
                progress.setValue(45)
                QApplication.processEvents()
                time.sleep(0.5)

                # تحديث شريط التقدم - إنشاء التقرير
                progress.setLabelText("📄 جاري إنشاء ملف PDF مع الإحصائيات...")
                progress.setValue(60)
                QApplication.processEvents()
                time.sleep(0.4)

                # استيراد ملف print313.py أو إنشاء التقرير محلياً
                success = False
                output_path = ""
                
                try:
                    import print313
                    
                    # تحديث تقدم إنشاء التقرير
                    progress.setLabelText("📊 جاري إنشاء التقرير مع الإحصائيات المفصلة...")
                    progress.setValue(70)
                    QApplication.processEvents()
                    
                    # استدعاء دالة إنشاء التقرير مع تمرير العنوان الصحيح من العنوان5
                    success, output_path, message = print313.print_exams_report(
                        parent=self,
                        level=None,  # جميع المستويات
                        report_title=title5,  # استخدام العنوان5 بدلاً من title2
                        subject_data=None
                    )
                    
                except ImportError:
                    # في حالة عدم وجود print313.py، نشتاء التقرير باستخدام print313 مبسط
                    progress.setLabelText("📋 جاري إنشاء تقرير مبسط...")
                    progress.setValue(70)
                    QApplication.processEvents()
                    
                    try:
                        from datetime import datetime
                        
                        # إنشاء مجلد التقارير
                        downloads_folder = get_downloads_folder()
                        reports_folder = os.path.join(downloads_folder, "تقارير برنامج المعين في الحراسة العامة")
                        os.makedirs(reports_folder, exist_ok=True)
                        
                        # إنشاء اسم ملف مع طابع زمني
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        output_filename = f"المحضر_الجماعي_{timestamp}.pdf"
                        output_path = os.path.join(reports_folder, output_filename)
                        
                        # هنا يمكن إضافة كود إنشاء PDF مبسط إذا لزم الأمر
                        success = True
                        
                    except Exception as e:
                        print(f"خطأ في إنشاء التقرير المبسط: {e}")
                        success = False

                # تحديث شريط التقدم - حفظ الملف
                progress.setLabelText("💾 جاري حفظ الملف...")
                progress.setValue(85)
                QApplication.processEvents()
                time.sleep(0.3)

                # تحديث شريط التقدم - فتح الملف
                progress.setLabelText("🚀 جاري فتح الملف...")
                progress.setValue(95)
                QApplication.processEvents()
                time.sleep(0.3)

                # تحديث شريط التقدم - اكتمال العملية
                progress.setLabelText("✅ اكتملت العملية بنجاح!")
                progress.setValue(100)
                QApplication.processEvents()
                time.sleep(0.6)

                if success:
                    # إنشاء رسالة النجاح التفصيلية مع الإحصائيات
                    success_message = f"تم إنشاء المحضر الجماعي للمترشحين بنجاح وفتحه.\n\n"
                    success_message += f"📊 ملخص التقرير:\n"
                    success_message += f"• العنوان المستخدم: {title5}\n"
                    success_message += f"• جميع المستويات ({total_levels} مستوى)\n"
                    success_message += f"• إجمالي المترشحين: {total_candidates}\n\n"
                    
                    # إضافة إحصائيات الجنس
                    if gender_stats:
                        success_message += f"📈 إحصائيات الجنس:\n"
                        for gender, count in gender_stats.items():
                            percentage = (count / total_candidates * 100) if total_candidates > 0 else 0
                            success_message += f"  • {gender}: {count} ({percentage:.1f}%)\n"
                        success_message += "\n"
                    
                    # إضافة إحصائيات العمر (أول 5 أعمار فقط لعدم إطالة الرسالة)
                    if age_stats:
                        success_message += f"🎂 إحصائيات العمر (عينة):\n"
                        count_displayed = 0
                        for age, count in age_stats.items():
                            if count_displayed >= 5:
                                success_message += f"  • ... والمزيد\n"
                                break
                            percentage = (count / total_candidates * 100) if total_candidates > 0 else 0
                            success_message += f"  • {age} سنة: {count} ({percentage:.1f}%)\n"
                            count_displayed += 1
                        success_message += "\n"
                    
                    # إضافة إحصائيات المؤسسة (أول 3 مؤسسات فقط)
                    if institution_stats:
                        success_message += f"🏫 إحصائيات المؤسسات (عينة):\n"
                        count_displayed = 0
                        for institution, count in institution_stats.items():
                            if count_displayed >= 3:
                                success_message += f"  • ... والمزيد\n"
                                break
                            percentage = (count / total_candidates * 100) if total_candidates > 0 else 0
                            success_message += f"  • {institution}: {count} ({percentage:.1f}%)\n"
                            count_displayed += 1
                        success_message += "\n"
                    
                    success_message += f"• يتضمن التقرير إحصائيات مفصلة لكل مستوى\n"
                    success_message += f"\n📁 مسار الملف:\n{output_path}"

                    self.show_message("نجاح", success_message, "success")
                else:
                    self.show_message("تنبيه", f"لم يتم إنشاء المحضر الجماعي: فشل في إنشاء التقرير", "warning")

            except Exception as e:
                self.show_message("خطأ", f"حدث خطأ أثناء إنشاء المحضر الجماعي للمترشحين: {str(e)}", "error")
            finally:
                # إغلاق شريط التقدم
                progress.close()

        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إنشاء المحضر الجماعي للمترشحين: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def calculate_age(self, birth_date_str):
        """حساب العمر من تاريخ الميلاد"""
        try:
            from datetime import datetime
            if not birth_date_str or birth_date_str.strip() == '':
                return 999  # قيمة كبيرة للتصنيف في النهاية
            
            # محاولة تحليل التاريخ بعدة تنسيقات
            formats = ['%Y-%m-%d', '%d/%m/%Y', '%d-%m-%Y', '%Y/%m/%d']
            birth_date = None
            
            for fmt in formats:
                try:
                    birth_date = datetime.strptime(birth_date_str.strip(), fmt)
                    break
                except ValueError:
                    continue
            
            if birth_date is None:
                return 999
            
            today = datetime.today()
            age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
            
            return age
        except:
            return 999

    def clear_fields(self):
        """مسح جميع الحقول"""
        for field in self.text_fields.values():
            field.clear()

    def save_changes(self):
        """حفظ التغييرات في قاعدة البيانات"""
        try:
            # التحقق من إدخال البيانات المطلوبة
            title1 = self.text_fields["العنوان1"].text().strip()
            title2 = self.text_fields["العنوان2"].text().strip()
            title3 = self.text_fields["العنوان3"].text().strip()
            title4 = self.text_fields["العنوان4"].text().strip()
            title5 = self.text_fields["العنوان5"].text().strip()

            if not title1 and not title2 and not title3 and not title4 and not title5:
                self.show_message("تنبيه", "لا توجد بيانات للحفظ.", "warning")
                return

            # إنشاء نافذة تأكيد مخصصة
            confirm_dialog = CustomMessageDialog(
                self,
                "تأكيد الحفظ",
                "هل أنت متأكد من حفظ التغييرات؟",
                "info"
            )

            if confirm_dialog.exec_() == QDialog.Accepted:
                # تحديث السجل في قاعدة البيانات
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # التحقق من وجود السجل (نبحث عن أول سجل)
                cursor.execute("SELECT * FROM جدول_الامتحان LIMIT 1")
                record = cursor.fetchone()

                if record:
                    # الحصول على مسار الخلفية الحالي
                    background_path = getattr(self, 'background_pdf_path', '') or ''

                    # تحديث السجل الموجود
                    cursor.execute("""
                        UPDATE جدول_الامتحان
                        SET العنوان1 = ?, العنوان2 = ?, العنوان3 = ?, العنوان4 = ?, العنوان5 = ?, مسار_خلفية_PDF = ?
                        WHERE id = ?
                    """, (title1, title2, title3, title4, title5, background_path, record[0]))
                    conn.commit()
                    self.show_message("نجاح", "تم حفظ التغييرات بنجاح.", "success")
                else:
                    # إضافة سجل جديد (هذا لن يحدث عادة لأننا نضيف سجلًا فارغًا عند إنشاء الجدول)
                    background_path = getattr(self, 'background_pdf_path', '') or ''
                    cursor.execute("""
                        INSERT INTO جدول_الامتحان (العنوان1, العنوان2, العنوان3, العنوان4, العنوان5, مسار_خلفية_PDF)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (title1, title2, title3, title4, title5, background_path))
                    conn.commit()
                    self.show_message("نجاح", "تم إضافة العناوين بنجاح.", "success")

                conn.close()
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء حفظ التغييرات: {str(e)}", "error")

    def add_background(self):
        """إضافة خلفية PDF للتقارير"""
        try:
            # تعيين عنوان النافذة
            self.setWindowTitle("إضافة خلفية PDF - إدارة التقارير واللوائح والاستدعاءات")

            # فتح متصفح ملفات PDF
            file_dialog = QFileDialog(self)
            file_dialog.setWindowTitle("اختيار ملف PDF للخلفية")
            file_dialog.setNameFilter("ملفات PDF (*.pdf)")
            file_dialog.setFileMode(QFileDialog.ExistingFile)
            file_dialog.setAcceptMode(QFileDialog.AcceptOpen)
            file_dialog.setLayoutDirection(Qt.RightToLeft)

            # تطبيق تنسيق مخصص لمتصفح الملفات
            file_dialog.setStyleSheet("""
                QFileDialog {
                    background-color: #f0f8ff;
                    color: black;
                    font-family: Calibri;
                    font-size: 12pt;
                }
                QFileDialog QListView {
                    background-color: white;
                    border: 1px solid #3498db;
                    border-radius: 5px;
                }
                QFileDialog QPushButton {
                    background-color: #3498db;
                    color: white;
                    border-radius: 5px;
                    padding: 8px 15px;
                    font-weight: bold;
                    min-height: 30px;
                    min-width: 100px;
                }
                QFileDialog QPushButton:hover {
                    background-color: #2980b9;
                }
            """)

            if file_dialog.exec_() == QFileDialog.Accepted:
                selected_files = file_dialog.selectedFiles()
                if selected_files:
                    pdf_path = selected_files[0]

                    # التحقق من أن الملف هو PDF صالح
                    if not pdf_path.lower().endswith('.pdf'):
                        self.show_message("خطأ", "الرجاء اختيار ملف PDF صالح.", "error")
                        return

                    # التحقق من وجود الملف
                    if not os.path.exists(pdf_path):
                        self.show_message("خطأ", "الملف المحدد غير موجود.", "error")
                        return

                    # حفظ مسار الملف
                    self.background_pdf_path = pdf_path

                    # إنشاء تقرير تجريبي لمعاينة النتيجة
                    self.create_test_report_with_background(pdf_path)

        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إضافة الخلفية: {str(e)}", "error")
        finally:
            # إعادة عنوان النافذة الأصلي
            self.setWindowTitle("إدارة التقارير واللوائح والاستدعاءات")

    def create_test_report_with_background(self, background_pdf_path):
        """إنشاء تقرير تجريبي مع الخلفية لمعاينة النتيجة"""
        try:
            # إنشاء شريط تقدم
            progress = QProgressDialog("جاري إنشاء تقرير تجريبي مع الخلفية...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("إنشاء تقرير تجريبي")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setAutoClose(True)
            progress.setAutoReset(True)
            progress.setMinimumWidth(400)

            # تحسين مظهر شريط التقدم
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #e67e22;
                    border-radius: 10px;
                    padding: 10px;
                }
                QLabel {
                    color: #d35400;
                    font-family: Calibri;
                    font-size: 12pt;
                    font-weight: bold;
                }
                QProgressBar {
                    border: 1px solid #e67e22;
                    border-radius: 5px;
                    background-color: #ecf0f1;
                    text-align: center;
                    color: black;
                    font-family: Calibri;
                    font-weight: bold;
                    min-height: 20px;
                }
                QProgressBar::chunk {
                    background-color: #e67e22;
                    width: 10px;
                    margin: 0.5px;
                }
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border-radius: 5px;
                    padding: 5px 10px;
                    font-family: Calibri;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)

            progress.setValue(0)
            progress.show()

            # تحديث شريط التقدم
            progress.setLabelText("جاري تحضير البيانات...")
            progress.setValue(20)
            QApplication.processEvents()
            time.sleep(0.3)

            # إنشاء تقرير تجريبي باستخدام PyPDF2 أو reportlab
            progress.setLabelText("جاري إنشاء التقرير مع الخلفية...")
            progress.setValue(50)
            QApplication.processEvents()

            # الحصول على مجلد التنزيلات
            downloads_folder = get_downloads_folder()
            reports_folder = os.path.join(downloads_folder, "تقارير برنامج المعين في الحراسة العامة")
            os.makedirs(reports_folder, exist_ok=True)

            # إنشاء اسم الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = os.path.join(reports_folder, f"تقرير_تجريبي_مع_خلفية_{timestamp}.pdf")

            # إنشاء تقرير تجريبي بسيط مع الخلفية
            success = self.create_simple_test_report(output_path, background_pdf_path)

            progress.setLabelText("جاري فتح التقرير...")
            progress.setValue(80)
            QApplication.processEvents()
            time.sleep(0.3)

            progress.setValue(100)
            QApplication.processEvents()
            time.sleep(0.5)

            if success:
                # فتح الملف
                try:
                    if sys.platform == 'win32':
                        os.startfile(output_path)
                    elif sys.platform == 'darwin':  # macOS
                        subprocess.call(['open', output_path])
                    else:  # Linux
                        subprocess.call(['xdg-open', output_path])
                except Exception as e:
                    print(f"خطأ في فتح الملف: {e}")

                self.show_message(
                    "نجح إضافة الخلفية!",
                    f"تم إنشاء تقرير تجريبي مع الخلفية بنجاح!\n\n"
                    f"📁 مسار الملف:\n{output_path}\n\n"
                    f"🎨 مسار ملف الخلفية:\n{background_pdf_path}\n\n"
                    f"💡 ملاحظة: سيتم استخدام هذه الخلفية في جميع التقارير المستقبلية.\n"
                    f"لا تنس حفظ التغييرات باستخدام زر 'حفظ'.",
                    "success"
                )
            else:
                self.show_message("خطأ", "فشل في إنشاء التقرير التجريبي.", "error")

        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إنشاء التقرير التجريبي: {str(e)}", "error")

    def create_simple_test_report(self, output_path, background_pdf_path):
        """إنشاء تقرير تجريبي بسيط مع خلفية PDF"""
        try:
            # محاولة استخدام PyPDF2 لدمج الخلفية
            try:
                from PyPDF2 import PdfWriter, PdfReader

                # إنشاء تقرير مؤقت باستخدام reportlab
                temp_report_path = output_path.replace('.pdf', '_temp.pdf')
                self.create_reportlab_test_report(temp_report_path, None)

                # قراءة ملف الخلفية
                with open(background_pdf_path, 'rb') as bg_file:
                    background_reader = PdfReader(bg_file)
                    background_page = background_reader.pages[0]  # استخدام الصفحة الأولى كخلفية

                # قراءة التقرير المؤقت
                with open(temp_report_path, 'rb') as temp_file:
                    temp_reader = PdfReader(temp_file)
                    writer = PdfWriter()

                    # دمج كل صفحة من التقرير مع الخلفية
                    for page in temp_reader.pages:
                        # إنشاء نسخة من صفحة الخلفية
                        new_page = background_page
                        new_page.merge_page(page)
                        writer.add_page(new_page)

                # حفظ الملف النهائي
                with open(output_path, 'wb') as output_file:
                    writer.write(output_file)

                # حذف الملف المؤقت
                if os.path.exists(temp_report_path):
                    os.remove(temp_report_path)

                return True

            except ImportError:
                print("PyPDF2 غير متوفر، سيتم إنشاء تقرير تجريبي بدون دمج الخلفية")
                # إنشاء تقرير تجريبي يوضح أن الخلفية ستُستخدم
                return self.create_reportlab_test_report_with_note(output_path, background_pdf_path)

            except Exception as e:
                print(f"خطأ في دمج الخلفية: {e}")
                # في حالة الفشل، إنشاء تقرير عادي مع ملاحظة
                return self.create_reportlab_test_report_with_note(output_path, background_pdf_path)

        except Exception as e:
            print(f"خطأ في إنشاء التقرير التجريبي: {e}")
            return False

    def create_reportlab_test_report(self, output_path, background_pdf_path=None):
        """إنشاء تقرير تجريبي باستخدام reportlab"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.pdfgen import canvas
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            import arabic_reshaper
            from bidi.algorithm import get_display

            # إنشاء ملف PDF
            c = canvas.Canvas(output_path, pagesize=A4)
            width, height = A4

            # محاولة تحميل خط عربي
            try:
                fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
                if os.path.exists(os.path.join(fonts_dir, 'arial.ttf')):
                    pdfmetrics.registerFont(TTFont('Arabic', os.path.join(fonts_dir, 'arial.ttf')))
                    font_name = 'Arabic'
                else:
                    font_name = 'Helvetica'
            except:
                font_name = 'Helvetica'

            # دالة لتحويل النص العربي
            def ar_text(text):
                try:
                    reshaped = arabic_reshaper.reshape(str(text))
                    return get_display(reshaped)
                except:
                    return str(text)

            # إضافة محتوى التقرير التجريبي
            c.setFont(font_name, 16)

            # العنوان
            title = ar_text("تقرير تجريبي مع خلفية PDF")
            c.drawRightString(width - 50, height - 100, title)

            # النص التوضيحي
            c.setFont(font_name, 12)
            content_lines = [
                "تم إنشاء هذا التقرير التجريبي لمعاينة الخلفية المضافة",
                "سيتم استخدام هذه الخلفية في جميع التقارير المستقبلية",
                "يمكنك الآن رؤية كيف ستبدو التقارير مع الخلفية الجديدة",
                "",
                f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                "برنامج المعين في الحراسة العامة"
            ]

            y_position = height - 150
            for line in content_lines:
                if line:
                    text = ar_text(line)
                    c.drawRightString(width - 50, y_position, text)
                y_position -= 25

            # إضافة إطار للصفحة
            c.setStrokeColorRGB(0.2, 0.4, 0.8)
            c.setLineWidth(2)
            c.rect(30, 30, width - 60, height - 60)

            # حفظ الملف
            c.save()
            return True

        except Exception as e:
            print(f"خطأ في إنشاء تقرير reportlab: {e}")
            return False

    def create_reportlab_test_report_with_note(self, output_path, background_pdf_path):
        """إنشاء تقرير تجريبي مع ملاحظة حول الخلفية"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.pdfgen import canvas
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            import arabic_reshaper
            from bidi.algorithm import get_display

            # إنشاء ملف PDF
            c = canvas.Canvas(output_path, pagesize=A4)
            width, height = A4

            # محاولة تحميل خط عربي
            try:
                fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
                if os.path.exists(os.path.join(fonts_dir, 'arial.ttf')):
                    pdfmetrics.registerFont(TTFont('Arabic', os.path.join(fonts_dir, 'arial.ttf')))
                    font_name = 'Arabic'
                else:
                    font_name = 'Helvetica'
            except:
                font_name = 'Helvetica'

            # دالة لتحويل النص العربي
            def ar_text(text):
                try:
                    reshaped = arabic_reshaper.reshape(str(text))
                    return get_display(reshaped)
                except:
                    return str(text)

            # إضافة محتوى التقرير التجريبي
            c.setFont(font_name, 16)

            # العنوان
            title = ar_text("تم حفظ خلفية PDF بنجاح!")
            c.drawRightString(width - 50, height - 100, title)

            # النص التوضيحي
            c.setFont(font_name, 12)
            content_lines = [
                "تم حفظ ملف الخلفية بنجاح في النظام",
                "سيتم استخدام هذه الخلفية في جميع التقارير المستقبلية",
                "",
                f"مسار ملف الخلفية:",
                f"{background_pdf_path}",
                "",
                "ملاحظة: لرؤية الخلفية الفعلية، يجب تثبيت مكتبة PyPDF2:",
                "pip install PyPDF2",
                "",
                f"تاريخ الإضافة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                "برنامج المعين في الحراسة العامة"
            ]

            y_position = height - 150
            for line in content_lines:
                if line:
                    text = ar_text(line)
                    c.drawRightString(width - 50, y_position, text)
                y_position -= 20

            # إضافة إطار للصفحة بلون مختلف
            c.setStrokeColorRGB(0.8, 0.4, 0.2)  # لون برتقالي
            c.setLineWidth(2)
            c.rect(30, 30, width - 60, height - 60)

            # إضافة رمز تحذير
            c.setFont(font_name, 24)
            warning_text = ar_text("⚠️")
            c.drawRightString(width - 50, height - 70, warning_text)

            # حفظ الملف
            c.save()
            return True

        except Exception as e:
            print(f"خطأ في إنشاء تقرير مع ملاحظة: {e}")
            return False

    def close_window(self):
        """إغلاق النافذة"""
        try:
            # إنشاء نافذة تأكيد مخصصة
            confirm_dialog = CustomMessageDialog(
                self,
                "تأكيد الإغلاق",
                "هل أنت متأكد من إغلاق النافذة؟\nسيتم فقدان أي بيانات غير محفوظة.",
                "warning"
            )

            if confirm_dialog.exec_() == QDialog.Accepted:
                self.accept()
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء إغلاق النافذة: {str(e)}", "error")

# للاختبار المستقل
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = Sub40Window()
    window.show()
    sys.exit(app.exec_())