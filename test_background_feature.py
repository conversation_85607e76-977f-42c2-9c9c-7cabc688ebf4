#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار ميزة إضافة خلفية PDF في sub40_window.py
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from sub40_window import Sub40Window

def test_background_feature():
    """اختبار ميزة إضافة الخلفية"""
    print("🧪 بدء اختبار ميزة إضافة خلفية PDF...")
    
    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)
    
    # إنشاء نافذة sub40
    window = Sub40Window()
    window.show()
    
    print("✅ تم فتح نافذة إدارة التقارير بنجاح!")
    print("📋 يمكنك الآن اختبار زر 'إضافة خلفية' الجديد")
    print("🎯 الخطوات:")
    print("   1. اضغط على زر 'إضافة خلفية'")
    print("   2. اختر ملف PDF للخلفية")
    print("   3. سيتم إنشاء تقرير تجريبي لمعاينة النتيجة")
    print("   4. احفظ التغييرات باستخدام زر 'حفظ'")
    
    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_background_feature()
