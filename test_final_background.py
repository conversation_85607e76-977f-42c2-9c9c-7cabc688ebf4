#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار نهائي لميزة إضافة خلفية PDF المحسنة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

def test_background_feature():
    """اختبار ميزة إضافة الخلفية"""
    try:
        print("🚀 بدء اختبار ميزة إضافة خلفية PDF...")
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # استيراد النافذة
        from sub40_window import Sub40Window
        
        # إنشاء النافذة
        window = Sub40Window()
        window.show()
        
        print("✅ تم فتح النافذة بنجاح!")
        print("📋 يمكنك الآن اختبار زر 'إضافة خلفية' الجديد")
        print("🎯 الخطوات:")
        print("   1. اضغط على زر 'إضافة خلفية' (اللون البرتقالي)")
        print("   2. اختر ملف PDF للخلفية من جهازك")
        print("   3. سيتم إنشاء تقرير تجريبي تلقائياً")
        print("   4. ستظهر الخلفية خلف النص في التقرير")
        print("   5. احفظ التغييرات باستخدام زر 'حفظ'")
        print("")
        print("💡 نصائح:")
        print("   - استخدم ملف PDF بخلفية ملونة أو بصورة لرؤية النتيجة بوضوح")
        print("   - تأكد من أن ملف PDF غير محمي بكلمة مرور")
        print("   - الخلفية ستظهر في جميع التقارير المستقبلية")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_background_feature()
