# دليل استخدام خلفية PDF في التقارير

## المشكلة الأصلية
كان زر "إضافة خلفية" يحفظ مسار ملف الخلفية في قاعدة البيانات، لكن التقارير الأخرى لا تستخدم هذه الخلفية المحفوظة.

## الحل المطبق

### 1. تحديث المكتبات المطلوبة
تم تحديث ملف `requirements.txt` ليشمل جميع المكتبات المطلوبة:
- PyPDF2 (لدمج ملفات PDF)
- reportlab (لإنشاء ملفات PDF)
- arabic-reshaper & python-bidi (لدعم النص العربي)
- Pillow (لمعالجة الصور)

### 2. إنشاء وحدة دمج الخلفية
تم إنشاء ملف `background_merger.py` الذي يحتوي على:
- `get_background_path()`: للحصول على مسار الخلفية من قاعدة البيانات
- `merge_pdf_with_background()`: لدمج أي تقرير مع الخلفية المحفوظة
- `create_test_report_with_background()`: لإنشاء تقرير تجريبي

### 3. تحسين وظيفة إضافة الخلفية
تم تحسين دالة `create_test_report_with_background` في `sub40_window.py` لاستخدام الوحدة الجديدة ولإظهار رسائل خطأ مفيدة أكثر.

## طريقة الاستخدام

### خطوة 1: تثبيت المكتبات المطلوبة
```bash
# تشغيل ملف الـ batch
install_requirements.bat

# أو تشغيل الأمر مباشرة
pip install -r requirements.txt
```

### خطوة 2: إضافة خلفية جديدة
1. افتح البرنامج واذهب إلى نافذة "إدارة التقارير واللوائح والاستدعاءات"
2. اضغط على زر "إضافة خلفية"
3. اختر ملف PDF للخلفية
4. سيتم إنشاء تقرير تجريبي تلقائياً لمعاينة النتيجة
5. لا تنس الضغط على "حفظ" لحفظ مسار الخلفية

### خطوة 3: استخدام الخلفية في التقارير الأخرى

#### الطريقة السهلة - تعديل ملفات الطباعة الموجودة:

```python
# في بداية ملف الطباعة، أضف:
from background_merger import merge_pdf_with_background
import os

# في نهاية دالة إنشاء التقرير، بدلاً من حفظ الملف مباشرة:
def create_report_with_background(original_output_path):
    # 1. إنشاء ملف مؤقت
    temp_path = original_output_path.replace('.pdf', '_temp.pdf')
    
    # 2. إنشاء التقرير العادي في الملف المؤقت
    # ... كودك الأصلي لإنشاء التقرير ...
    # c.save() أو writer.write() -> احفظ في temp_path
    
    # 3. دمج مع الخلفية
    success = merge_pdf_with_background(
        content_pdf_path=temp_path,
        output_pdf_path=original_output_path,
        db_path="data.db"
    )
    
    # 4. حذف الملف المؤقت
    if os.path.exists(temp_path):
        os.remove(temp_path)
    
    return success
```

## الملفات الجديدة المضافة

1. **background_merger.py** - الوحدة الرئيسية لدمج الخلفية
2. **install_requirements.bat** - سكريبت تثبيت المكتبات المطلوبة
3. **background_usage_example.py** - أمثلة على الاستخدام
4. **README_خلفية_PDF.md** - هذا الملف

## استكشاف الأخطاء

### إذا لم تظهر الخلفية:
1. تأكد من تثبيت جميع المكتبات المطلوبة
2. تأكد من أن ملف الخلفية PDF صحيح وغير تالف
3. تأكد من وجود مساحة كافية على القرص الصلب
4. تحقق من أن مسار الخلفية محفوظ بشكل صحيح (استخدم زر "حفظ")

### رسائل الخطأ الشائعة:
- **"PyPDF2 غير متوفر"**: قم بتشغيل `install_requirements.bat`
- **"ملف الخلفية فارغ"**: اختر ملف PDF صحيح للخلفية
- **"فشل في دمج الخلفية"**: تحقق من صحة ملف الخلفية وإذونات الكتابة

## اختبار الميزة

لاختبار الميزة بسرعة:
```bash
python background_merger.py
```

هذا سيقوم بتشغيل اختبار سريع للتأكد من عمل الوحدة بشكل صحيح.

## ملاحظات مهمة

1. **النسخ الاحتياطية**: احتفظ بنسخة احتياطية من ملفات الطباعة قبل تعديلها
2. **حجم الملفات**: الخلفية ستزيد من حجم ملفات التقارير
3. **الأداء**: دمج الخلفية قد يستغرق وقتاً إضافياً لإنشاء التقارير الكبيرة
4. **التوافق**: تأكد من أن ملف الخلفية PDF متوافق ولا يحتوي على حماية

## الدعم الفني

إذا واجهت أي مشاكل:
1. تحقق من رسائل الخطأ في وحدة التحكم
2. تأكد من تثبيت جميع المكتبات المطلوبة
3. جرب إنشاء تقرير تجريبي أولاً للتأكد من عمل الميزة
4. راجع ملف `background_usage_example.py` للحصول على أمثلة عملية
