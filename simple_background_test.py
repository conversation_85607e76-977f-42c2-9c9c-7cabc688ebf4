#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار بسيط جداً لدمج خلفية PDF
"""

import os
import io
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4

def test_simple_background():
    """اختبار بسيط لإنشاء PDF مع خلفية"""
    try:
        print("🧪 بدء الاختبار البسيط...")
        
        # إنشاء مجلد للاختبار
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        test_folder = os.path.join(desktop_path, "اختبار_خلفية_PDF")
        os.makedirs(test_folder, exist_ok=True)
        
        # 1. إنشاء خلفية بسيطة
        bg_path = os.path.join(test_folder, "خلفية.pdf")
        c = canvas.Canvas(bg_path, pagesize=A4)
        width, height = A4
        
        # خلفية ملونة
        c.setFillColorRGB(0.9, 0.95, 1.0)
        c.rect(0, 0, width, height, fill=1)
        
        # شبكة
        c.setStrokeColorRGB(0.8, 0.8, 0.8)
        c.setLineWidth(0.5)
        for x in range(0, int(width), 100):
            c.line(x, 0, x, height)
        for y in range(0, int(height), 100):
            c.line(0, y, width, y)
        
        # نص في الخلفية
        c.setFillColorRGB(0.7, 0.7, 0.7)
        c.setFont("Helvetica", 24)
        c.drawCentredText(width/2, height/2, "خلفية تجريبية")
        
        c.save()
        print(f"✅ تم إنشاء الخلفية: {bg_path}")
        
        # 2. إنشاء تقرير مع الخلفية
        output_path = os.path.join(test_folder, "تقرير_مع_خلفية.pdf")
        
        # استيراد PyPDF2
        from PyPDF2 import PdfReader, PdfWriter
        
        # قراءة الخلفية
        reader_bg = PdfReader(bg_path)
        bg_page = reader_bg.pages[0]
        mediabox = bg_page.mediabox
        page_width, page_height = float(mediabox.width), float(mediabox.height)
        
        # إنشاء محتوى في الذاكرة
        packet = io.BytesIO()
        c = canvas.Canvas(packet, pagesize=(page_width, page_height))
        
        # إضافة نص
        c.setFillColorRGB(0, 0, 0)
        c.setFont("Helvetica-Bold", 20)
        c.drawCentredText(page_width/2, page_height - 100, "تقرير تجريبي")
        
        c.setFont("Helvetica", 14)
        c.drawCentredText(page_width/2, page_height - 150, "هذا النص يظهر فوق الخلفية")
        
        # مربع ملون
        c.setFillColorRGB(1, 1, 0, alpha=0.8)
        c.rect(100, page_height - 300, page_width - 200, 80, fill=1)
        
        c.setFillColorRGB(0, 0, 0)
        c.drawCentredText(page_width/2, page_height - 260, "مربع أصفر فوق الخلفية")
        
        c.showPage()
        c.save()
        packet.seek(0)
        
        # دمج المحتوى مع الخلفية
        reader_overlay = PdfReader(packet)
        overlay_page = reader_overlay.pages[0]
        
        bg_page.merge_page(overlay_page)
        
        writer = PdfWriter()
        writer.add_page(bg_page)
        
        with open(output_path, "wb") as f:
            writer.write(f)
        
        print(f"✅ تم إنشاء التقرير: {output_path}")
        
        # فتح الملف
        try:
            os.startfile(output_path)
            print("📂 تم فتح الملف للمشاهدة")
        except:
            print("⚠️ تعذر فتح الملف تلقائياً")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 بدء الاختبار...")
    success = test_simple_background()
    if success:
        print("🎉 نجح الاختبار!")
    else:
        print("❌ فشل الاختبار!")
